
 /* This will let you use the .remove() function later on */
 if (!('remove' in Element.prototype)) {
   Element.prototype.remove = function () {
     if (this.parentNode) {
       this.parentNode.removeChild(this);
     }
   };
 }

 mapboxgl.accessToken = 'pk.eyJ1IjoiZGltZGV2IiwiYSI6ImNqc3lyd24zdTBlcmg0N3JuZzlwZDJhOWIifQ.8ZEtwLi2xwaP-4Zfl8-4Qw';

 /**
  * Add the map to the page
  */
 var map = new mapboxgl.Map({
   container: 'map',
   style: 'mapbox://styles/mapbox/satellite-v9',
   center: [23.46980094909668,40.04713153119762],
   zoom: 13,
   scrollZoom: true
 });

 var stores = {
   'type': 'FeatureCollection',
   'features': [
     {
       'type': 'Feature',
       'geometry': {
         'type': 'Point',
         'coordinates': [23.4677843,40.04712]
       },
       'properties': {
         'title': '<PERSON>riopigi - see where',
         'mobile': '+30 6937 110 873',
         'phone': '+30 23740 51189',
         'city': 'Kriopigi',
         'country': 'Greece',
         'googlelink': ' "https://maps.app.goo.gl/NHbnXdbSBcPatCGn6" ',
         'description': 'Kriopigi, Halkidiki'
       }
     },
     {
       'type': 'Feature',
       'geometry': {
         'type': 'Point',
         'coordinates': [23.43430995941162,40.10020026665549]
       },
       'properties': {
         'title': 'Afitos - see where',
         'mobile': '+30 6937 110 873',
         'phone': '+30 23740 51189',
         'city': 'Afitos',
         'country': 'Greece',
         'googlelink': 'https://goo.gl/maps/VCdcMkivN9ZZNYDe7',
         'description': 'Afitos, Halkidiki'
       }
     }

       ]
   };

 /**
  * Assign a unique id to each store. You'll use this `id`
  * later to associate each point on the map with a listing
  * in the sidebar.
  */
 stores.features.forEach(function (store, i) {
   store.properties.id = i;
 });

 /**
  * Wait until the map loads to make changes to the map.
  */
 map.on('load', function (e) {
   map.addLayer({
       "id": "locations",
       "type": "symbol",
       /* Add a GeoJSON source containing place coordinates and information. */
       "source": {
         "type": "geojson",
         "data": stores
       },
       "layout": {
         "icon-image": "marker-15",
         "icon-allow-overlap": true,
       }
     });
   /**
    * This is where your '.addLayer()' used to be, instead
    * add only the source without styling a layer
    */
   map.addSource('places', {
     'type': 'geojson',
     'data': stores
   });

   /**
    * Add all the things to the page:
    * - The location listings on the side of the page
    * - The markers onto the map
    */
   buildLocationList(stores);
   addMarkers();
 });



 /**
  * Add a marker to the map for every store listing.
  **/
 function addMarkers() {
   /* For each feature in the GeoJSON object above: */
   stores.features.forEach(function (marker) {
     /* Create a div element for the marker. */
     var el = document.createElement('div');
     /* Assign a unique `id` to the marker. */
     el.id = 'marker-' + marker.properties.id;
     /* Assign the `marker` class to each marker for styling. */
     el.className = 'marker';

     /**
      * Create a marker using the div element
      * defined above and add it to the map.
      **/
     new mapboxgl.Marker(el, { offset: [0, -23] })
       .setLngLat(marker.geometry.coordinates)
       .addTo(map);

     /**
      * Listen to the element and when it is clicked, do three things:
      * 1. Fly to the point
      * 2. Close all other popups and display popup for clicked store
      * 3. Highlight listing in sidebar (and remove highlight for all other listings)
      **/
     el.addEventListener('click', function (e) {
       /* Fly to the point */
       flyToStore(marker);
       /* Close all other popups and display popup for clicked store */
       createPopUp(marker);
       /* Highlight listing in sidebar */
       var activeItem = document.getElementsByClassName('active');
       e.stopPropagation();
       if (activeItem[0]) {
         activeItem[0].classList.remove('active');
       }
       var listing = document.getElementById(
         'listing-' + marker.properties.id
       );
       listing.classList.add('active');
     });
   });
 }

 /**
  * Add a listing for each store to the sidebar.
  **/
 function buildLocationList(data) {
   data.features.forEach(function (store, i) {
     /**
      * Create a shortcut for `store.properties`,
      * which will be used several times below.
      **/
     var prop = store.properties;

     /* Add a new listing section to the sidebar. */
var listings = document.getElementById('listings');
     

     /* Add the link to the individual listing created above. */
     var link = listings.appendChild(document.createElement('a'));
     link.href = '#';
     link.className = 'flex justify-center flex-1 h-full bg-white font-bebas p-4 hover:bg-gray-400 focus:bg-gray-400 text-3xl';
     link.id = 'link-' + prop.id;
     link.innerHTML = prop.title;

     /* Add details to the individual listing. */
     

     /**
      * Listen to the element and when it is clicked, do four things:
      * 1. Update the `currentFeature` to the store associated with the clicked link
      * 2. Fly to the point
      * 3. Close all other popups and display popup for clicked store
      * 4. Highlight listing in sidebar (and remove highlight for all other listings)
      **/
     link.addEventListener('click', function (e) {
       for (var i = 0; i < data.features.length; i++) {
         if (this.id === 'link-' + data.features[i].properties.id) {
           var clickedListing = data.features[i];
           flyToStore(clickedListing);
           createPopUp(clickedListing);
         }
       }
       var activeItem = document.getElementsByClassName('active');
       if (activeItem[0]) {
         activeItem[0].classList.remove('active');
       }
       this.parentNode.classList.add('active');
     });
   });
 }

 /**
  * Use Mapbox GL JS's `flyTo` to move the camera smoothly
  * a given center point.
  **/
 function flyToStore(currentFeature) {
   map.flyTo({
     center: currentFeature.geometry.coordinates,
     zoom: 15
   });
 }

 /**
  * Create a Mapbox GL JS `Popup`.
  **/
 function createPopUp(currentFeature) {
   var popUps = document.getElementsByClassName('mapboxgl-popup');
   if (popUps[0]) popUps[0].remove();
   var popup = new mapboxgl.Popup({ closeOnClick: false })
     .setLngLat(currentFeature.geometry.coordinates)
     .setHTML(
       '<h3 class="font-bebas">' + currentFeature.properties.title + '</h3>' +
         '<h4>' 
          + currentFeature.properties.mobile + 
          '</br>' + currentFeature.properties.phone +
          '</br> <a href=' +  currentFeature.properties.googlelink + ' target="_blank" class=" text-blue-800 hover:text-purple-800 focus:text-purple-800 hover:underline focus:underline">Google Directions</a>' +
         '</h4>'
     )
     .addTo(map);
 }
