---
---

<!DOCTYPE html>
<html lang="en">

{% include head.html %}

<body>





<div class="page w-full">
    <div class="page-wrapper flex flex-wrap justify-center">


{{content}}

    </div>
</div>


{% unless page.section == "frontpage" or page.section == "kriopigi" or page.section == "afitos" %}
<script src="/assets/js/optimized/js-all-mappages.js"></script>
<style>

    .marker {
        background-image: url('/assets/mapbox-icon.png');
        background-size: cover;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
      }

 </style>




{%else%}
<script src="/assets/js/optimized/js-all.js"></script>
{% endunless %}
 
{% include google-analytics.html %}



 <script src="https://cdn.plyr.io/3.6.12/plyr.polyfilled.js"></script>
    <script>
 const players = Array.from(document.querySelectorAll('#player')).map((p) => new Plyr(p));
        // Expose player so it can be used from the console
        window.player = player;
    </script>
</body>
</html>