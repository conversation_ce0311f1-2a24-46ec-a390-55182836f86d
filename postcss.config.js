module.exports = {
    plugins: [
        require("postcss-easy-import"),
        require("tailwindcss")("./tailwind.config.js"),
        require("postcss-nested"),
        require("autoprefixer"),
        require('@fullhuman/postcss-purgecss')({
            content: [
                './_site/**/*.html',
            '/_site/assets/js/optimized/js-all.js',
            '/_site/assets/js/optimized/js-all-mappages.js'],
            safelist: {
                deep: [/mapbox/,/pswp/,/lazy-background/,/cc-/],
                greedy: [/mapbox/,/cc-/]
              },
            fontFace: true,
            defaultExtractor: (content) =>
            content.match(/[\w-/:]+(?<!:)/g) || [],
        }),
        require('cssnano')({
            preset: 'default',
        }),
    ]
};