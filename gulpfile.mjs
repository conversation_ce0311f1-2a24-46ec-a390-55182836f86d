// Modern gulpfile.mjs with updated dependencies
// Replaces gulp-responsive with sharp for better Node.js v20 compatibility

import gulp from 'gulp';
const { series, parallel, src, dest, task } = gulp;
import browserSync from 'browser-sync';
import image from 'gulp-image';
import rename from 'gulp-rename';
import fs from 'node:fs';
import path from 'node:path';
import concat from 'gulp-concat';
import gulpif from 'gulp-if';
import recursiveReadSync from 'recursive-readdir-sync';
import imgsize from 'image-size';
import merge from 'deepmerge';
import yaml from 'js-yaml';
import pump from 'pump';
import uglify from 'gulp-uglify';
import purify from 'gulp-purifycss';
import nano from 'gulp-cssnano';
import include from 'gulp-include';
import rollup from 'gulp-rollup';
import sass from 'gulp-sass';
import * as dartSass from 'sass';
import postcss from 'gulp-postcss';
import autoprefixer from 'autoprefixer';
import pxtorem from 'postcss-pxtorem';
import orderedValues from 'postcss-ordered-values';
import colorHexAlpha from "postcss-color-hex-alpha";
import responsiveType from "postcss-responsive-type";
import { createDebugger } from 'postcss-debug';
import sharp from 'sharp';
import { Transform } from 'stream';

// Configure gulp-sass to use Dart Sass
const sassCompiler = sass(dartSass);

// Modern responsive image processing using sharp
function createResponsiveTransform(configs, options = {}) {
  return new Transform({
    objectMode: true,
    async transform(file, encoding, callback) {
      if (file.isNull()) {
        return callback(null, file);
      }

      if (file.isStream()) {
        return callback(new Error('Streaming not supported'));
      }

      try {
        const image = sharp(file.contents);
        const metadata = await image.metadata();

        for (const config of configs) {
          const outputImage = image.clone();

          // Apply transformations
          if (config.width || config.height) {
            outputImage.resize(config.width, config.height, {
              withoutEnlargement: !options.errorOnEnlargement,
              fit: 'inside'
            });
          }

          // Apply format conversion
          if (config.format === 'webp') {
            outputImage.webp({ quality: options.quality || 80 });
          } else if (config.format === 'jpeg') {
            outputImage.jpeg({ quality: options.quality || 80 });
          }

          // Generate output buffer
          const outputBuffer = await outputImage.toBuffer();

          // Create new file for each variant
          const newFile = file.clone();
          newFile.contents = outputBuffer;

          // Apply rename configuration
          if (config.rename) {
            const parsedPath = path.parse(file.relative);
            if (config.rename.suffix) {
              parsedPath.name += config.rename.suffix;
            }
            if (config.rename.extname) {
              parsedPath.ext = config.rename.extname;
            }
            newFile.path = path.join(file.base, path.format(parsedPath));
          }

          this.push(newFile);
        }

        callback();
      } catch (error) {
        callback(error);
      }
    }
  });
}

// Basic image optimization task
gulp.task('images', function () {
  return gulp.src('./assets/**/*.*')
    .pipe(image({
      pngquant: true,
      optipng: false,
      zopflipng: true,
      jpegRecompress: false,
      jpegoptim: true,
      mozjpeg: true,
      gifsicle: true,
      svgo: true,
      concurrent: 10
    }))
    .pipe(gulp.dest('./assets'));
});

// Modern WebP slideshow generation
gulp.task('images-all-webp-slideshow', function () {
  const configs = [
    { width: 1, height: 1, rename: { suffix: '-thumb', extname: '.webp' }, format: 'webp' },
    { width: 268, rename: { suffix: '-268', extname: '.webp' }, format: 'webp' },
    { width: 552, height: 330, rename: { suffix: '-552', extname: '.webp' }, format: 'webp' },
    { width: 960, rename: { suffix: '-960', extname: '.webp' }, format: 'webp' },
    { width: 1440, rename: { suffix: '-1440', extname: '.webp' }, format: 'webp' },
    { width: 2000, rename: { suffix: '-2000', extname: '.webp' }, format: 'webp' }
  ];

  return gulp.src(['_resources/uploaded/slideshow/**/*.{png,jpg,jpeg}'])
    .pipe(createResponsiveTransform(configs, {
      errorOnEnlargement: false,
      quality: 80
    }))
    .pipe(gulp.dest('assets/slideshow/'));
});

// Modern WebP placeholder generation
gulp.task('images-all-webp-placeholder', function () {
  const configs = [
    { width: 1, height: 1, rename: { suffix: '-thumb', extname: '.webp' }, format: 'webp' },
    { width: 268, rename: { suffix: '-medium', extname: '.webp' }, format: 'webp' },
    { width: 552, rename: { suffix: '-large', extname: '.webp' }, format: 'webp' },
    { height: 960, rename: { suffix: '-portrait', extname: '.webp' }, format: 'webp' },
    { width: 1440, rename: { suffix: '-full', extname: '.webp' }, format: 'webp' }
  ];

  return gulp.src(['_resources/uploaded/rooms/placeholder/**/*.{png,jpg}'])
    .pipe(createResponsiveTransform(configs, {
      errorOnEnlargement: false,
      quality: 80
    }))
    .pipe(gulp.dest('assets/rooms2/placeholder'));
});

// Modern WebP room images generation
gulp.task('images-all-webp', function () {
  const configs = [
    { width: 1, height: 1, rename: { suffix: '-thumb', extname: '.webp' }, format: 'webp' },
    { width: 268, rename: { suffix: '-medium', extname: '.webp' }, format: 'webp' },
    { width: 552, rename: { suffix: '-large', extname: '.webp' }, format: 'webp' },
    { height: 960, rename: { suffix: '-portrait', extname: '.webp' }, format: 'webp' },
    { width: 1440, rename: { suffix: '-full', extname: '.webp' }, format: 'webp' }
  ];

  return gulp.src(['_resources/uploaded/rooms/**/*.{png,jpg}'])
    .pipe(createResponsiveTransform(configs, {
      errorOnEnlargement: false,
      quality: 80
    }))
    .pipe(gulp.dest('assets/rooms2/'));
});

// Modern JPEG generation
gulp.task('images-all-jpg', function () {
  const configs = [
    { width: 1, height: 1, rename: { suffix: '-thumb' }, format: 'jpeg' },
    { width: 268, rename: { suffix: '-medium' }, format: 'jpeg' },
    { width: 552, rename: { suffix: '-large' }, format: 'jpeg' },
    { height: 960, rename: { suffix: '-portrait' }, format: 'jpeg' },
    { width: 1440, rename: { suffix: '-full' }, format: 'jpeg' }
  ];

  return gulp.src(['_resources/uploaded/rooms/**/*.{png,jpg}'])
    .pipe(createResponsiveTransform(configs, {
      errorOnEnlargement: false,
      quality: 80
    }))
    .pipe(gulp.dest('assets/rooms2/'));
});

// Photo indexing functionality (unchanged)
const walkPhotos = (path, index) => {
  const directory = fs.readdirSync(path);

  for (var i = 0; i < directory.length; i++) {
    const album = directory[i];

    // Skip hidden files like .DS_Store
    if (album.startsWith('.')) {
      continue;
    }
    const dirname = album;
    const photos = recursiveReadSync(path + '/' + album);
    const contains = [];

    for (var j = 0; j < photos.length; j++) {
      const photo = photos[j];
      const file = photo.split('/').pop();

      if (fs.statSync(photo).isDirectory()) { continue; }

      const dimensions = imgsize(photo);
      var orientvar = 'landscape'

      if (dimensions.width < dimensions.height) {
        var orientvar = 'portrait';
      }

      contains.push({
        filename: file,
        width: dimensions.width || null,
        height: dimensions.height || null,
        orientation: orientvar
      });
    }

    index[dirname] = {
      contents: contains
    };
  }

  for (var album in index) {
    if (!index.hasOwnProperty(album)) { continue; }
    index[album].contents = index[album].contents.sort((a, b) => {
      if (a.date < b.date) { return -1; }
      if (a.date > b.date) { return 1; }
      return 0;
    });
  }
};

gulp.task('photos', function () {
  let index = {};
  const generatedIndex = {};
  try {
    index = fs.readFileSync('_data/photos/index.yml', { encoding: 'utf8' });
    index = yaml.load(index); // Updated from deprecated safeLoad
  }
  catch (e) {
    if (e.code === 'ENOENT') {
      console.log('No original index found; will create one.');
    }
    else {
      throw e;
    }
  }
  walkPhotos('_resources/uploaded/rooms', generatedIndex);
  const mergedIndex = merge(index, generatedIndex);

  fs.writeFileSync('_data/photos/index.yml', yaml.dump(mergedIndex)); // Updated from deprecated safeDump
  return new Promise(function (resolve, reject) {
    console.log("Photos indexed");
    resolve();
  });
});

gulp.task('photos-resized', function () {
  let index = {};
  const generatedIndex = {};
  walkPhotos('assets/rooms2', generatedIndex);
  const mergedIndex = merge(index, generatedIndex);

  fs.writeFileSync('_data/photos/index-resized.yml', yaml.dump(mergedIndex));
});

// JavaScript processing tasks (unchanged)
gulp.task('js-compress', function (cb) {
  pump([
    gulp.src('_site/js/js-all.js'),
    uglify(),
    gulp.dest('assets/js/optimized')
  ], cb);
});

gulp.task('maincss', function () {
  return gulp.src(['./_site/assets/css/styles.css'])
    .pipe(gulp.dest('assets/css/optimized'))
    .pipe(gulp.dest('_includes/css'));
});

gulp.task('js', function () {
  return gulp.src(['./assets/js/_entry.js'])
    .pipe(include({
      extensions: 'js',
      hardFail: true,
      separateInputs: true,
      includePaths: [
        process.cwd() + '/node_modules' // Updated from __dirname
      ]
    }))
    .pipe(rename('js-all.js'))
    .pipe(gulp.dest('assets/js/optimized'))
});

gulp.task('js-mappages', function () {
  return gulp.src(['./assets/js/_entrymappages.js'])
    .pipe(include({
      extensions: 'js',
      hardFail: true,
      separateInputs: true,
      includePaths: [
        process.cwd() + '/node_modules' // Updated from __dirname
      ]
    }))
    .pipe(rename('js-all-mappages.js'))
    .pipe(gulp.dest('assets/js/optimized'))
});

gulp.task('bundle', function () {
  return gulp.src('assets/js/optimized/js-all.js')
    .pipe(rollup({
      input: 'assets/js/optimized/js-all.js',
      output: {
        file: 'bundle.js',
        format: 'cjs'
      }
    }))
    .pipe(gulp.dest('assets/js/optimized'))
});
