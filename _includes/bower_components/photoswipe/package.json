{"name": "photoswipe", "version": "4.1.2", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "grunt jshint"}, "devDependencies": {"grunt": "~0.4.5", "grunt-autoprefixer": "^3.0.3", "grunt-aws-s3": "^0.14.2", "grunt-contrib-clean": "~0.7.0", "grunt-contrib-concat": "~0.5.1", "grunt-contrib-copy": "^0.8.2", "grunt-contrib-cssmin": "^0.14.0", "grunt-contrib-jshint": "~0.11.3", "grunt-contrib-uglify": "~0.11.0", "grunt-contrib-watch": "~0.6.1", "grunt-jekyll": "~0.4.3", "grunt-sass": "^1.1.0", "grunt-svgmin": "^3.1.0"}, "repository": {"type": "git", "url": "git://github.com/dimsemenov/Photoswipe.git"}, "description": "JavaScript gallery", "bugs": {"url": "https://github.com/dimsemenov/Photoswipe/issues"}, "homepage": "http://photoswipe.com", "main": "dist/photoswipe.js", "keywords": ["gallery", "lightbox", "photo", "image", "touch", "swipe", "zoom"], "author": "<PERSON> (http://dimsemenov.com)", "license": "MIT"}