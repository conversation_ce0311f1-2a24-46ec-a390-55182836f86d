{"name": "photoswipe", "homepage": "http://photoswipe.com", "authors": ["<PERSON> <<EMAIL>> (http://dimsemenov.com)"], "description": "Touch-friendly image gallery for mobile and desktop. Without dependencies.", "main": ["dist/photoswipe.js", "dist/photoswipe.css", "dist/photoswipe-ui-default.js", "dist/default-skin/default-skin.css", "dist/default-skin/default-skin.png", "dist/default-skin/default-skin.svg", "dist/default-skin/preloader.gif"], "keywords": ["image", "gallery", "lightbox", "swipe", "touch"], "repository": {"type": "git", "url": "git://github.com/dimsemenov/PhotoSwipe.git"}, "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "website"], "version": "4.1.2", "_release": "4.1.2", "_resolution": {"type": "version", "tag": "v4.1.2", "commit": "b6fdb82ed0b5b635c0943098f3046581a02ead77"}, "_source": "https://github.com/dimsemenov/PhotoSwipe.git", "_target": "^4.1.2", "_originalSource": "photoswipe", "_direct": true}