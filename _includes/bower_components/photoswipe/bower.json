{"name": "photoswipe", "homepage": "http://photoswipe.com", "authors": ["<PERSON> <<EMAIL>> (http://dimsemenov.com)"], "description": "Touch-friendly image gallery for mobile and desktop. Without dependencies.", "main": ["dist/photoswipe.js", "dist/photoswipe.css", "dist/photoswipe-ui-default.js", "dist/default-skin/default-skin.css", "dist/default-skin/default-skin.png", "dist/default-skin/default-skin.svg", "dist/default-skin/preloader.gif"], "keywords": ["image", "gallery", "lightbox", "swipe", "touch"], "repository": {"type": "git", "url": "git://github.com/dimsemenov/PhotoSwipe.git"}, "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "website"]}