{"name": "parsleyjs", "version": "2.0.6", "ignore": ["**/.*", "build", "test", "doc", "*.md", ".json", "*.html", "AUTHORS.txt", "Gruntfile.js", "package.json"], "devDependencies": {"jquery": "~1.10.0", "validator.js": "~1.0.0", "requirejs": "~2.1.0", "bootstrap": "~3.0.3", "sinonjs": "~1.7.3", "uwidget": "~0.0.1"}, "keywords": ["parsley", "form", "validation", "html5", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://guillaumepotier.com/"}, "license": "MIT", "main": "dist/parsley.js", "homepage": "https://github.com/guillaumepotier/Parsley.js", "_release": "2.0.6", "_resolution": {"type": "version", "tag": "2.0.6", "commit": "cdd392291f71f5b60ab093f851ff63d5de93b94f"}, "_source": "git://github.com/guillaumepotier/Parsley.js.git", "_target": "~2.0.6", "_originalSource": "parsleyjs", "_direct": true}