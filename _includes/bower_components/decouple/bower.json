{"name": "decouple", "repository": "**************:pazguille/mediumjs.git", "description": "Decouple the DOM events from expensive functions.", "author": "@pazguille <<EMAIL>>", "version": "0.0.1", "ignore": [".*", "package.json", "component.json", "node_modules", "gulpfile.js", "**/.*", "bower_components", "test", "tests"], "main": "dist/decouple", "license": "MIT", "homepage": "https://github.com/pazguille/decouple", "moduleType": ["adecouplemd", "globals", "node"], "keywords": ["decouple", "events", "dom events"], "authors": ["@pazguille <<EMAIL>>"]}