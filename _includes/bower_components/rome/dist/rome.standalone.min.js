// rome@v1.2.1, MIT licensed. https://github.com/bevacqua/rome
!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e;"undefined"!=typeof window?e=window:"undefined"!=typeof global?e=global:"undefined"!=typeof self&&(e=self),e.rome=t()}}(function(){return function t(e,n,r){function o(a,s){if(!n[a]){if(!e[a]){var u="function"==typeof require&&require;if(!s&&u)return u(a,!0);if(i)return i(a,!0);throw new Error("Cannot find module '"+a+"'")}var l=n[a]={exports:{}};e[a][0].call(l.exports,function(t){var n=e[a][1][t];return o(n?n:t)},l,l.exports,t,e,n,r)}return n[a].exports}for(var i="function"==typeof require&&require,a=0;a<r.length;a++)o(r[a]);return o}({1:[function(t,e){function n(){}var r=e.exports={};r.nextTick=function(){var t="undefined"!=typeof window&&window.setImmediate,e="undefined"!=typeof window&&window.postMessage&&window.addEventListener;if(t)return function(t){return window.setImmediate(t)};if(e){var n=[];return window.addEventListener("message",function(t){var e=t.source;if((e===window||null===e)&&"process-tick"===t.data&&(t.stopPropagation(),n.length>0)){var r=n.shift();r()}},!0),function(t){n.push(t),window.postMessage("process-tick","*")}}return function(t){setTimeout(t,0)}}(),r.title="browser",r.browser=!0,r.env={},r.argv=[],r.on=n,r.addListener=n,r.once=n,r.off=n,r.removeListener=n,r.removeAllListeners=n,r.emit=n,r.binding=function(){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(){throw new Error("process.chdir is not supported")}},{}],2:[function(t,e){e.exports=t("./src/contra.emitter.js")},{"./src/contra.emitter.js":3}],3:[function(t,e){(function(t){!function(n,r){"use strict";function o(t,e){return Array.prototype.slice.call(t,e)}function i(t,e,n){t&&s(function(){t.apply(n||null,e||[])})}function a(t,e){var n=e||{},a={};return t===r&&(t={}),t.on=function(e,n){return a[e]?a[e].push(n):a[e]=[n],t},t.once=function(e,n){return n._once=!0,t.on(e,n),t},t.off=function(e,n){var r=arguments.length;if(1===r)delete a[e];else if(0===r)a={};else{var o=a[e];if(!o)return t;o.splice(o.indexOf(n),1)}return t},t.emit=function(){var e=o(arguments),r=e.shift(),s=a[r];if("error"===r&&n.throws!==!1&&!s)throw 1===e.length?e[0]:e;return s?(a[r]=s.filter(function(r){return n.async?i(r,e,t):r.apply(t,e),!r._once}),t):t},t}var s,u=""+r,l="function"==typeof setImmediate;s=l?function(t){setImmediate(t)}:typeof t!==u&&t.nextTick?t.nextTick:function(t){setTimeout(t,0)},typeof e!==u&&e.exports?e.exports=a:(n.contra=n.contra||{},n.contra.emitter=a)}(this)}).call(this,t("FWaASH"))},{FWaASH:1}],4:[function(t,e){function n(t,e,n){var s=!0,u=!0;if(!o(t))throw new TypeError;return n===!1?s=!1:i(n)&&(s="leading"in n?n.leading:s,u="trailing"in n?n.trailing:u),a.leading=s,a.maxWait=e,a.trailing=u,r(t,e,a)}var r=t("lodash.debounce"),o=t("lodash.isfunction"),i=t("lodash.isobject"),a={leading:!1,maxWait:0,trailing:!1};e.exports=n},{"lodash.debounce":5,"lodash.isfunction":8,"lodash.isobject":9}],5:[function(t,e){function n(t,e,n){var s,u,l,c,d,f,m,p=0,y=!1,h=!0;if(!r(t))throw new TypeError;if(e=a(0,e)||0,n===!0){var v=!0;h=!1}else o(n)&&(v=n.leading,y="maxWait"in n&&(a(e,n.maxWait)||0),h="trailing"in n?n.trailing:h);var b=function(){var n=e-(i()-c);if(0>=n){u&&clearTimeout(u);var r=m;u=f=m=void 0,r&&(p=i(),l=t.apply(d,s),f||u||(s=d=null))}else f=setTimeout(b,n)},g=function(){f&&clearTimeout(f),u=f=m=void 0,(h||y!==e)&&(p=i(),l=t.apply(d,s),f||u||(s=d=null))};return function(){if(s=arguments,c=i(),d=this,m=h&&(f||!v),y===!1)var n=v&&!f;else{u||v||(p=c);var r=y-(c-p),o=0>=r;o?(u&&(u=clearTimeout(u)),p=c,l=t.apply(d,s)):u||(u=setTimeout(g,r))}return o&&f?f=clearTimeout(f):f||e===y||(f=setTimeout(b,e)),n&&(o=!0,l=t.apply(d,s)),!o||f||u||(s=d=null),l}}var r=t("lodash.isfunction"),o=t("lodash.isobject"),i=t("lodash.now"),a=Math.max;e.exports=n},{"lodash.isfunction":8,"lodash.isobject":9,"lodash.now":6}],6:[function(t,e){var n=t("lodash._isnative"),r=n(r=Date.now)&&r||function(){return(new Date).getTime()};e.exports=r},{"lodash._isnative":7}],7:[function(t,e){function n(t){return"function"==typeof t&&i.test(t)}var r=Object.prototype,o=r.toString,i=RegExp("^"+String(o).replace(/[.*+?^${}()|[\]\\]/g,"\\$&").replace(/toString| for [^\]]+/g,".*?")+"$");e.exports=n},{}],8:[function(t,e){function n(t){return"function"==typeof t}e.exports=n},{}],9:[function(t,e){function n(t){return!(!t||!r[typeof t])}var r=t("lodash._objecttypes");e.exports=n},{"lodash._objecttypes":10}],10:[function(t,e){var n={"boolean":!1,"function":!0,object:!0,number:!1,string:!1,undefined:!1};e.exports=n},{}],11:[function(t,e){for(var n=t("performance-now"),r="undefined"==typeof window?{}:window,o=["moz","webkit"],i="AnimationFrame",a=r["request"+i],s=r["cancel"+i]||r["cancelRequest"+i],u=0;u<o.length&&!a;u++)a=r[o[u]+"Request"+i],s=r[o[u]+"Cancel"+i]||r[o[u]+"CancelRequest"+i];if(!a||!s){var l=0,c=0,d=[],f=1e3/60;a=function(t){if(0===d.length){var e=n(),r=Math.max(0,f-(e-l));l=r+e,setTimeout(function(){var t=d.slice(0);d.length=0;for(var e=0;e<t.length;e++)t[e].cancelled||t[e].callback(l)},r)}return d.push({handle:++c,callback:t,cancelled:!1}),c},s=function(t){for(var e=0;e<d.length;e++)d[e].handle===t&&(d[e].cancelled=!0)}}e.exports=function(){return a.apply(r,arguments)},e.exports.cancel=function(){s.apply(r,arguments)}},{"performance-now":12}],12:[function(t,e){(function(t){(function(){var n,r,o;"undefined"!=typeof performance&&null!==performance&&performance.now?e.exports=function(){return performance.now()}:"undefined"!=typeof t&&null!==t&&t.hrtime?(e.exports=function(){return(n()-o)/1e6},r=t.hrtime,n=function(){var t;return t=r(),1e9*t[0]+t[1]},o=n()):Date.now?(e.exports=function(){return Date.now()-o},o=Date.now()):(e.exports=function(){return(new Date).getTime()-o},o=(new Date).getTime())}).call(this)}).call(this,t("FWaASH"))},{FWaASH:1}],13:[function(t,e){"use strict";function n(t,e){var n=u[t.id];return n&&n[e.id]}function r(t,e){var n=u[t.id];n||(n=u[t.id]={});var r=i(e);n[e.id]=r,t.on("data",r),t.on("destroyed",o.bind(null,t,e))}function o(t,e){var n=u[t.id];if(n){var r=n[e.id];t.off("data",r),delete n[e.id]}}function i(t){return function(){t.refresh()}}function a(t,e){s(e.associated)||n(t,e)||r(t,e)}var s=t("./isInput"),u={};e.exports={add:a,remove:o}},{"./isInput":24}],14:[function(t,e){"use strict";function n(t){function e(){return xe}function n(n){return ue=c(n||t,xe),de||(de=a({className:ue.styles.container})),me=r,fe=r,pe=r,ye=r,ue.appendTo.appendChild(de),G(de),Ee=!1,le=ue.initialValue?ue.initialValue:d.moment(),ce=le.clone(),xe.container=de,xe.destroyed=!1,xe.destroy=y.bind(xe,!1),xe.emitValues=W,xe.getDate=ie,xe.getDateString=ae,xe.getMoment=se,xe.hide=j,xe.options=v,xe.options.reset=b,xe.refresh=$,xe.restore=e,xe.setValue=z,xe.show=N,M(),h(),xe.emit("ready",l(ue)),xe}function y(t){return de&&de.parentNode.removeChild(de),ue&&h(!0),xe.destroyed=!0,xe.destroy=e,xe.emitValues=e,xe.getDate=p,xe.getDateString=p,xe.getMoment=p,xe.hide=e,xe.options=e,xe.options.reset=e,xe.refresh=e,xe.restore=n,xe.setValue=e,xe.show=e,t!==!0&&xe.emit("destroyed"),xe.off(),xe}function h(t){var e=t?"remove":"add";ue.autoHideOnBlur&&m[e](document,"focusin",I),ue.autoHideOnClick&&m[e](document,"click",H)}function v(t){return 0===arguments.length?l(ue):(y(),n(t),xe)}function b(){return v({})}function g(){Ee||(Ee=!0,w(),x(),xe.emit("render"))}function w(){function t(t){var e=a({className:ue.styles.month,parent:he});0===t&&(ve=a({type:"button",className:ue.styles.back,attributes:{type:"button"},parent:e})),t===ue.monthsInCalendar-1&&(be=a({type:"button",className:ue.styles.next,attributes:{type:"button"},parent:e}));var n,r=a({className:ue.styles.monthLabel,parent:e}),o=a({type:"table",className:ue.styles.dayTable,parent:e}),i=a({type:"thead",className:ue.styles.dayHead,parent:o}),s=a({type:"tr",className:ue.styles.dayRow,parent:i}),u=a({type:"tbody",className:ue.styles.dayBody,parent:o});for(n=0;ke>n;n++)a({type:"th",className:ue.styles.dayHeadElem,parent:s,text:Ae[E(n)]});u.setAttribute(Te,t),De.push({label:r,body:u})}if(ue.date){var e;for(De=[],he=a({className:ue.styles.date,parent:de}),e=0;e<ue.monthsInCalendar;e++)t(e);m.add(ve,"click",S),m.add(be,"click",V),m.add(he,"click",te)}}function x(){if(ue.time&&ue.timeInterval){var t=a({className:ue.styles.time,parent:de});ge=a({className:ue.styles.selectedTime,parent:t,text:le.format(ue.timeFormat)}),m.add(ge,"click",A),we=a({className:ue.styles.timeList,parent:t}),m.add(we,"click",oe);for(var e=d.moment("00:00:00","HH:mm:ss"),n=e.clone().add(1,"days");e.isBefore(n);)a({className:ue.styles.timeOption,parent:we,text:e.format(ue.timeFormat)}),e.add(ue.timeInterval,"seconds")}}function E(t,e){var n=e?-1:1,r=t+ue.weekStart*n;return(r>=ke||0>r)&&(r+=ke*-n),r}function T(){if(ue.time&&Ee){var t,e,n,r,o=we.children,i=o.length;for(r=0;i>r;r++)n=o[r],e=d.moment(s(n),ue.timeFormat),t=re(le.clone(),e),n.style.display=Q(t,!1,ue.timeValidator)?"block":"none"}}function A(t){var e="boolean"==typeof t?t:"none"===we.style.display;e?k():D()}function k(){we&&(we.style.display="block")}function D(){we&&(we.style.display="none")}function O(){de.style.display="inline-block",xe.emit("show")}function M(){de.style.display="none",xe.emit("hide")}function N(){return g(),$(),A(!ue.date),O(),xe}function j(){return D(),i(M),xe}function C(){D();var t=f.contains(de,ue.styles.positioned);return t&&i(M),xe}function F(t){var e=t.target;if(e===xe.associated)return!0;for(;e;){if(e===de)return!0;e=e.parentNode}}function I(t){F(t)||C()}function H(t){F(t)||C()}function S(){B("subtract")}function V(){B("add")}function B(t){var e,n="add"===t?-1:1,r=ue.monthsInCalendar+n*ne(ye);ce[t]("months",r),e=U(ce.clone()),le=e||le,e&&(ce=e.clone()),L()}function L(t){q(),_(),t!==!0&&W(),T()}function q(){function t(t,e){var n=ce.clone().add(e,"months");s(t.label,n.format(ue.monthFormat)),G(t.body)}if(ue.date&&Ee){var e=ce.year(),n=ce.month(),r=ce.date();if(r!==pe||n!==me||e!==fe){var o=Y();if(pe=ce.date(),me=ce.month(),fe=ce.year(),o)return void P();De.forEach(t),J()}}}function P(){function t(t){var e,n=[];for(e=0;e<t.length;e++)n.push(t[e]);return n}function e(e){return t(e.children)}function n(t){return!f.contains(t,ue.styles.dayPrevMonth)&&!f.contains(t,ue.styles.dayNextMonth)}var r=ce.date()-1;ee(!1),De.forEach(function(o){var i;R(o.date,ce)&&(i=t(o.body.children).map(e),i=Array.prototype.concat.apply([],i).filter(n),ee(i[r]))})}function Y(){function t(t){return fe?R(t.date,ce):!1}return De.some(t)}function R(t,e){return t&&e&&t.year()===e.year()&&t.month()===e.month()}function _(){ue.time&&Ee&&s(ge,le.format(ue.timeFormat))}function W(){return xe.emit("data",ae()),xe.emit("year",le.year()),xe.emit("month",le.month()),xe.emit("day",le.day()),xe.emit("time",le.format(ue.timeFormat)),xe}function $(){return fe=!1,me=!1,pe=!1,L(!0),xe}function z(t){var e=u(t,ue.inputFormat);if(null!==e)return le=U(e)||le,ce=le.clone(),L(!0),xe}function G(t,e){for(;t&&t.firstChild;)t.removeChild(t.firstChild);e===!0&&t.parentNode.removeChild(t)}function J(){var t;for(t=0;t<ue.monthsInCalendar;t++)K(t)}function K(t){function e(t){var e,r,o;for(e=0;e<t.length;e++)f.children.length===ke&&(f=a({type:"tr",className:ue.styles.dayRow,parent:i.body})),r=t.base.clone().add(e,"days"),o=a({type:"td",parent:f,text:r.format(ue.dayFormat),className:n(r,t.cell.join(" ").split(" ")).join(" ")}),t.selectable&&r.date()===l&&ee(o)}function n(t,e){return Q(t,!0,ue.dateValidator)||e.push(y),e}function r(t,e){return t&&e.push(ue.styles.dayConcealed),e}var o,i=De[t],s=ce.clone().add(t,"months"),u=s.daysInMonth(),l=s.month()!==le.month()?-1:le.date(),c=s.clone().date(1),d=E(c.day(),!0),f=a({type:"tr",className:ue.styles.dayRow,parent:i.body}),m=r(0!==t,[ue.styles.dayBodyElem,ue.styles.dayPrevMonth]),p=r(t!==ue.monthsInCalendar-1,[ue.styles.dayBodyElem,ue.styles.dayNextMonth]),y=ue.styles.dayDisabled;e({base:c.clone().subtract(d,"days"),length:d,cell:m}),e({base:c.clone(),length:u,cell:[ue.styles.dayBodyElem],selectable:!0}),o=c.clone().add(u,"days"),e({base:o,length:ke-f.children.length,cell:p}),ve.disabled=!Q(c,!0),be.disabled=!Q(o,!0),i.date=s.clone()}function Q(t,e,n){var r=ue.min?e?ue.min.clone().startOf("day"):ue.min:!1,o=ue.max?e?ue.max.clone().endOf("day"):ue.max:!1;if(r&&t.isBefore(r))return!1;if(o&&t.isAfter(o))return!1;var i=(n||Function.prototype).call(xe,t.toDate());return i!==!1}function U(t){if(ue.min&&t.isBefore(ue.min))return U(ue.min.clone());if(ue.max&&t.isAfter(ue.max))return U(ue.max.clone());var e=t.clone().subtract(1,"days");return Z(e,t,"add")?X(e):(e=t.clone(),Z(e,t,"subtract")?X(e):void 0)}function X(t){var e,n=t.clone().subtract(ue.timeInterval,"seconds"),r=Math.ceil(Oe/ue.timeInterval);for(e=0;r>e;e++)if(n.add(ue.timeInterval,"seconds"),n.date()>t.date()&&n.subtract(1,"days"),ue.timeValidator.call(xe,n.toDate())!==!1)return n}function Z(t,e,n){for(var r=!1;r===!1&&(t[n](1,"days"),t.month()===e.month());)r=ue.dateValidator.call(xe,t.toDate());return r!==!1}function te(t){var e=t.target;if(!f.contains(e,ue.styles.dayDisabled)&&f.contains(e,ue.styles.dayBodyElem)){var n=parseInt(s(e),10),r=f.contains(e,ue.styles.dayPrevMonth),o=f.contains(e,ue.styles.dayNextMonth),i=ne(e)-ne(ye);le.add(i,"months"),(r||o)&&le.add(r?-1:1,"months"),ee(e),le.date(n),re(le,U(le)||le),ce=le.clone(),ue.autoClose&&C(),L()}}function ee(t){ye&&f.remove(ye,ue.styles.selectedDay),t&&f.add(t,ue.styles.selectedDay),ye=t}function ne(t){for(var e;t&&t.getAttribute;){if(e=t.getAttribute(Te),"string"==typeof e)return parseInt(e,10);t=t.parentNode}return 0}function re(t,e){return t.hour(e.hour()).minute(e.minute()).second(e.second()),t}function oe(t){var e=t.target;if(f.contains(e,ue.styles.timeOption)){var n=d.moment(s(e),ue.timeFormat);re(le,n),ce=le.clone(),W(),_(),!ue.date&&ue.autoClose?C():D()}}function ie(){return le.toDate()}function ae(t){return le.format(t||ue.inputFormat)}function se(){return le.clone()}var ue,le,ce,de,fe,me,pe,ye,he,ve,be,ge,we,xe=o({}),Ee=!1,Te="data-rome-offset",Ae=d.moment.weekdaysMin(),ke=Ae.length,De=[],Oe=86400;return y(!0),i(function(){n()}),xe}var r,o=t("contra.emitter"),i=t("raf"),a=t("./dom"),s=t("./text"),u=t("./parse"),l=t("./clone"),c=t("./defaults"),d=t("./momentum"),f=t("./classes"),m=t("./events"),p=t("./noop");e.exports=n},{"./classes":15,"./clone":16,"./defaults":18,"./dom":19,"./events":20,"./momentum":25,"./noop":26,"./parse":27,"./text":40,"contra.emitter":2,raf:11}],15:[function(t,e){"use strict";function n(t){return t.className.replace(s,"").split(u)}function r(t,e){t.className=e.join(" ")}function o(t,e){var n=i(t,e);n.push(e),r(t,n)}function i(t,e){var o=n(t),i=o.indexOf(e);return-1!==i&&(o.splice(i,1),r(t,o)),o}function a(t,e){return-1!==n(t).indexOf(e)}var s=/^\s+|\s+$/g,u=/\s+/;e.exports={add:o,remove:i,contains:a}},{}],16:[function(t,e){"use strict";function n(t){var e,o={};for(var i in t)e=t[i],o[i]=e?r.isMoment(e)?e.clone():e._isStylesConfiguration?n(e):e:e;return o}var r=t("./momentum");e.exports=n},{"./momentum":25}],17:[function(t,e){"use strict";function n(t,e){var n,s=r.find(t);return s?s:(n=a(t)?o(t,e):i(t,e),n.associated=t,r.assign(t,n),n)}var r=t("./index"),o=t("./input"),i=t("./inline"),a=t("./isInput");e.exports=n},{"./index":21,"./inline":22,"./input":23,"./isInput":24}],18:[function(t,e){"use strict";function n(t,e){var n,a,s=t||{};if(s.autoHideOnClick===a&&(s.autoHideOnClick=!0),s.autoHideOnBlur===a&&(s.autoHideOnBlur=!0),s.autoClose===a&&(s.autoClose=!0),s.appendTo===a&&(s.appendTo=document.body),"parent"===s.appendTo){if(!o(e.associated))throw new Error("Inline calendars must be appended to a parent node explicitly.");s.appendTo=e.associated.parentNode}if(s.invalidate===a&&(s.invalidate=!0),s.required===a&&(s.required=!1),s.date===a&&(s.date=!0),s.time===a&&(s.time=!0),s.date===!1&&s.time===!1)throw new Error("At least one of `date` or `time` must be `true`.");if(s.inputFormat===a&&(s.inputFormat=s.date&&s.time?"YYYY-MM-DD HH:mm":s.date?"YYYY-MM-DD":"HH:mm"),s.initialValue=s.initialValue===a?null:r(s.initialValue,s.inputFormat),s.min=s.min===a?null:r(s.min,s.inputFormat),s.max=s.max===a?null:r(s.max,s.inputFormat),s.timeInterval===a&&(s.timeInterval=1800),s.min&&s.max)if(s.max.isBefore(s.min)&&(n=s.max,s.max=s.min,s.min=n),s.date===!0){if(s.max.clone().subtract(1,"days").isBefore(s.min))throw new Error("`max` must be at least one day after `min`")}else if(1e3*s.timeInterval-s.min%(1e3*s.timeInterval)>s.max-s.min)throw new Error("`min` to `max` range must allow for at least one time option that matches `timeInterval`");s.dateValidator===a&&(s.dateValidator=Function.prototype),s.timeValidator===a&&(s.timeValidator=Function.prototype),s.timeFormat===a&&(s.timeFormat="HH:mm"),s.weekStart===a&&(s.weekStart=i.moment().weekday(0).day()),s.monthsInCalendar===a&&(s.monthsInCalendar=1),s.monthFormat===a&&(s.monthFormat="MMMM YYYY"),s.dayFormat===a&&(s.dayFormat="DD"),s.styles===a&&(s.styles={}),s.styles._isStylesConfiguration=!0;var u=s.styles;return u.back===a&&(u.back="rd-back"),u.container===a&&(u.container="rd-container"),u.positioned===a&&(u.positioned="rd-container-attachment"),u.date===a&&(u.date="rd-date"),u.dayBody===a&&(u.dayBody="rd-days-body"),u.dayBodyElem===a&&(u.dayBodyElem="rd-day-body"),u.dayPrevMonth===a&&(u.dayPrevMonth="rd-day-prev-month"),u.dayNextMonth===a&&(u.dayNextMonth="rd-day-next-month"),u.dayDisabled===a&&(u.dayDisabled="rd-day-disabled"),u.dayConcealed===a&&(u.dayConcealed="rd-day-concealed"),u.dayHead===a&&(u.dayHead="rd-days-head"),u.dayHeadElem===a&&(u.dayHeadElem="rd-day-head"),u.dayRow===a&&(u.dayRow="rd-days-row"),u.dayTable===a&&(u.dayTable="rd-days"),u.month===a&&(u.month="rd-month"),u.monthLabel===a&&(u.monthLabel="rd-month-label"),u.next===a&&(u.next="rd-next"),u.selectedDay===a&&(u.selectedDay="rd-day-selected"),u.selectedTime===a&&(u.selectedTime="rd-time-selected"),u.time===a&&(u.time="rd-time"),u.timeList===a&&(u.timeList="rd-time-list"),u.timeOption===a&&(u.timeOption="rd-time-option"),s}var r=t("./parse"),o=t("./isInput"),i=t("./momentum");e.exports=n},{"./isInput":24,"./momentum":25,"./parse":27}],19:[function(t,e){"use strict";function n(t){var e=t||{};e.type||(e.type="div");var n=document.createElement(e.type);return e.className&&(n.className=e.className),e.text&&(n.innerText=n.textContent=e.text),e.attributes&&Object.keys(e.attributes).forEach(function(t){n.setAttribute(t,e.attributes[t])}),e.parent&&e.parent.appendChild(n),n}e.exports=n},{}],20:[function(t,e){"use strict";function n(t,e,n){return t.addEventListener(e,n)}function r(t,e,n){return t.attachEvent("on"+e,function(e){var r=e||window.event;r.target=r.target||r.srcElement,r.preventDefault=r.preventDefault||function(){r.returnValue=!1},r.stopPropagation=r.stopPropagation||function(){r.cancelBubble=!0},n.call(t,r)})}function o(t,e,n){return t.removeEventListener(e,n)}function i(t,e,n){return t.detachEvent("on"+e,n)}var a=n,s=o;window.addEventListener||(a=r),window.removeEventListener||(s=i),e.exports={add:a,remove:s}},{}],21:[function(t,e){"use strict";function n(t){if("number"!=typeof t&&t&&t.getAttribute)return n(t.getAttribute(i));var e=a[t];return e!==o?e:null}function r(t,e){t.setAttribute(i,e.id=a.push(e)-1)}var o,i="data-rome-id",a=[];e.exports={find:n,assign:r}},{}],22:[function(t,e){"use strict";function n(t,e){function n(){r(a.show)}var i=e||{};i.appendTo=t;var a=o(i).on("ready",n);return a}var r=t("raf"),o=t("./calendar");e.exports=n},{"./calendar":14,raf:11}],23:[function(t,e){"use strict";function n(t,e){function n(e){E=i(e),u.add(k.container,E.styles.positioned),l.add(k.container,"mousedown",p),l.add(k.container,"click",m),k.getDate=x(k.getDate),k.getDateString=x(k.getDateString),k.getMoment=x(k.getMoment),E.initialValue&&(t.value=E.initialValue.format(E.inputFormat)),k.on("data",g),k.on("show",O),f(),D()}function c(){f(!0),o(d)}function d(){k.once("ready",n),k.once("destroyed",c)}function f(e){var n=e?"remove":"add";l[n](t,"click",h),l[n](t,"touchend",h),l[n](t,"focusin",h),l[n](t,"change",D),l[n](t,"keypress",D),l[n](t,"keydown",D),l[n](t,"input",D),E.invalidate&&l[n](t,"blur",y),l[n](window,"resize",O)}function m(){A=!0,t.focus(),A=!1}function p(){function t(){T=!1}T=!0,o(t)}function y(){T||w()||k.emitValues()}function h(){A||k.show()}function v(){var e=t.getBoundingClientRect(),n=document.body.scrollTop||document.documentElement.scrollTop;k.container.style.top=e.top+n+t.offsetHeight+"px",k.container.style.left=e.left+"px"}function b(){var e=t.value.trim();if(!w()){var n=s.moment(e,E.inputFormat);k.setValue(n)}}function g(e){t.value=e}function w(){return E.required===!1&&""===t.value.trim()}function x(t){return function(){return w()?null:t.apply(this,arguments)}}var E,T,A,k=a(e),D=r(b,50),O=r(v,30);return d(),k}var r=t("lodash.throttle"),o=t("raf"),i=t("./clone"),a=t("./calendar"),s=t("./momentum"),u=t("./classes"),l=t("./events");e.exports=n},{"./calendar":14,"./classes":15,"./clone":16,"./events":20,"./momentum":25,"lodash.throttle":4,raf:11}],24:[function(t,e){"use strict";function n(t){return t&&t.nodeName&&"input"===t.nodeName.toLowerCase()}e.exports=n},{}],25:[function(t,e){"use strict";function n(t){return t&&Object.prototype.hasOwnProperty.call(t,"_isAMomentObject")}var r={moment:null,isMoment:n};e.exports=r},{}],26:[function(t,e){"use strict";function n(){}e.exports=n},{}],27:[function(t,e){"use strict";function n(t,e){return"string"==typeof t?o.moment(t,e):"[object Date]"===Object.prototype.toString.call(t)?o.moment(t):o.isMoment(t)?t.clone():void 0}function r(t,e){var r=n(t,"string"==typeof e?e:null);return r&&r.isValid()?r:null}var o=t("./momentum");e.exports=r},{"./momentum":25}],28:[function(){Array.prototype.every||(Array.prototype.every=function(t,e){var n,r;if(null==this)throw new TypeError("this is null or not defined");var o=Object(this),i=o.length>>>0;if("function"!=typeof t)throw new TypeError(t+" is not a function");for(arguments.length>1&&(n=e),r=0;i>r;){if(r in o){var a=t.call(n,o[r],r,o);if(!a)return!1}r++}return!0})},{}],29:[function(){Array.prototype.filter||(Array.prototype.filter=function(t,e){var n=[];return this.forEach(function(r,o,i){t.call(e,r,o,i)&&n.push(r)},e),n})},{}],30:[function(){Array.prototype.forEach||(Array.prototype.forEach=function(t,e){if(void 0===this||null===this||"function"!=typeof t)throw new TypeError;for(var n=this,r=n.length,o=0;r>o;o++)o in n&&t.call(e,n[o],o,n)})},{}],31:[function(){Array.prototype.indexOf||(Array.prototype.indexOf=function(t,e){if(void 0===this||null===this)throw new TypeError;var n=this.length;for(e=+e||0,1/0===Math.abs(e)?e=0:0>e&&(e+=n,0>e&&(e=0));n>e;e++)if(this[e]===t)return e;return-1})},{}],32:[function(){Array.isArray||(Array.isArray=function(t){return""+t!==t&&"[object Array]"===Object.prototype.toString.call(t)})},{}],33:[function(){Array.prototype.map||(Array.prototype.map=function(t,e){var n,r,o;if(null==this)throw new TypeError("this is null or not defined");var i=Object(this),a=i.length>>>0;if("function"!=typeof t)throw new TypeError(t+" is not a function");for(arguments.length>1&&(n=e),r=new Array(a),o=0;a>o;)o in i&&(r[o]=t.call(n,i[o],o,i)),o++;return r})},{}],34:[function(){Array.prototype.some||(Array.prototype.some=function(t,e){var n,r;if(null==this)throw new TypeError("this is null or not defined");var o=Object(this),i=o.length>>>0;if("function"!=typeof t)throw new TypeError(t+" is not a function");for(arguments.length>1&&(n=e),r=0;i>r;){if(r in o){var a=t.call(n,o[r],r,o);if(a)return!0}r++}return!1})},{}],35:[function(){Function.prototype.bind||(Function.prototype.bind=function(t){if("function"!=typeof this)throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var e=Array.prototype.slice.call(arguments,1),n=this,r=function(){},o=function(){var o=this instanceof r&&t?this:t,i=e.concat(Array.prototype.slice.call(arguments));return n.apply(o,i)};return r.prototype=this.prototype,o.prototype=new r,o})},{}],36:[function(){Object.keys||(Object.keys=function(){"use strict";var t=Object.prototype.hasOwnProperty,e=!{toString:null}.propertyIsEnumerable("toString"),n=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],r=n.length;return function(o){if("object"!=typeof o&&("function"!=typeof o||null===o))throw new TypeError("Object.keys called on non-object");var i,a,s=[];for(i in o)t.call(o,i)&&s.push(i);if(e)for(a=0;r>a;a++)t.call(o,n[a])&&s.push(n[a]);return s}}())},{}],37:[function(){String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")})},{}],38:[function(t,e){"use strict";t("./polyfills/function.bind"),t("./polyfills/array.foreach"),t("./polyfills/array.map"),t("./polyfills/array.filter"),t("./polyfills/array.isarray"),t("./polyfills/array.indexof"),t("./polyfills/array.every"),t("./polyfills/array.some"),t("./polyfills/string.trim"),t("./polyfills/object.keys");var n=t("./core"),r=t("./index"),o=t("./use");n.use=o.bind(n),n.find=r.find,n.val=t("./validators"),e.exports=n},{"./core":17,"./index":21,"./polyfills/array.every":28,"./polyfills/array.filter":29,"./polyfills/array.foreach":30,"./polyfills/array.indexof":31,"./polyfills/array.isarray":32,"./polyfills/array.map":33,"./polyfills/array.some":34,"./polyfills/function.bind":35,"./polyfills/object.keys":36,"./polyfills/string.trim":37,"./use":41,"./validators":42}],39:[function(t,e){(function(n){var r=t("./rome"),o=t("./momentum");if(r.use(n.moment),void 0===o.moment)throw new Error("rome depends on moment.js, you can get it at http://momentjs.com, or you could use the bundled distribution file instead.");e.exports=r}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./momentum":25,"./rome":38}],40:[function(t,e){"use strict";function n(t,e){return 2===arguments.length&&(t.innerText=t.textContent=e),t.innerText||t.textContent}e.exports=n},{}],41:[function(t,e){"use strict";function n(t){this.moment=r.moment=t}var r=t("./momentum");e.exports=n},{"./momentum":25}],42:[function(t,e){"use strict";function n(t){return function(e){var n=i(e);return function(r){var s=o.find(e),u=i(r),l=n||s&&s.getMoment();return l?(s&&a.add(this,s),t(u,l)):!0}}}function r(t,e){return function(n,r){function s(t){var e,n,r=o.find(t);return r?e=n=r.getMoment():Array.isArray(t)?(e=t[0],n=t[1]):e=n=t,r&&a.add(r,this),{start:i(e).startOf("day").toDate(),end:i(n).endOf("day").toDate()}}var u,l=arguments.length;return Array.isArray(n)?u=n:1===l?u=[n]:2===l&&(u=[[n,r]]),function(n){return u.map(s.bind(this))[t](e.bind(this,n))}}}var o=t("./index"),i=t("./parse"),a=t("./association"),s=n(function(t,e){return t>=e}),u=n(function(t,e){return t>e}),l=n(function(t,e){return e>=t}),c=n(function(t,e){return e>t}),d=r("every",function(t,e){return e.start>t||e.end<t}),f=r("some",function(t,e){return e.start<=t&&e.end>=t});e.exports={afterEq:s,after:u,beforeEq:l,before:c,except:d,only:f}},{"./association":13,"./index":21,"./parse":27}]},{},[39])(39)});