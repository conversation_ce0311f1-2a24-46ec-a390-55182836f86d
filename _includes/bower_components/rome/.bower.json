{"name": "rome", "description": "Customizable date (and time) picker. Opt-in UI, no jQuery!", "homepage": "https://github.com/bevacqua/rome", "version": "1.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ponyfoo.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/bevacqua/rome.git"}, "bugs": {"url": "https://github.com/bevacqua/rome/issues"}, "main": "dist/rome.js", "ignore": [".*", "package.json", "node_modules", "src", "index.html", "example", "resources", "gulpfile.js"], "dependencies": {}, "_release": "1.2.1", "_resolution": {"type": "version", "tag": "v1.2.1", "commit": "0ae2e773d2cdee3c92ab74f25b9a686f47bf6f22"}, "_source": "git://github.com/bevacqua/rome.git", "_target": "~1.2.1", "_originalSource": "rome", "_direct": true}