<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>swipe - Documentation</title>

    <script src="scripts/prettify/prettify.js"></script>
    <script src="scripts/prettify/lang-css.js"></script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <link type="text/css" rel="stylesheet" href="styles/prettify.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
</head>
<body>

<input type="checkbox" id="nav-trigger" class="nav-trigger" />
<label for="nav-trigger" class="navicon-button x">
  <div class="navicon"></div>
</label>

<label for="nav-trigger" class="overlay"></label>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="$.html">$</a></li><li><a href="$.fn.html">fn</a></li><li><a href="$.fn.swipe.html">swipe</a><ul class='methods'><li data-type='method'><a href="$.fn.swipe.html#destroy">destroy</a></li><li data-type='method'><a href="$.fn.swipe.html#disable">disable</a></li><li data-type='method'><a href="$.fn.swipe.html#enable">enable</a></li><li data-type='method'><a href="$.fn.swipe.html#option">option</a></li></ul></li></ul><h3>Events</h3><ul><li><a href="$.fn.swipe.html#event:click">click</a></li><li><a href="$.fn.swipe.html#event:doubleTap">doubleTap</a></li><li><a href="$.fn.swipe.html#event:hold">hold</a></li><li><a href="$.fn.swipe.html#event:longTap">longTap</a></li><li><a href="$.fn.swipe.html#event:pinchIn">pinchIn</a></li><li><a href="$.fn.swipe.html#event:pinchOut">pinchOut</a></li><li><a href="$.fn.swipe.html#event:pinchStatus">pinchStatus</a></li><li><a href="$.fn.swipe.html#event:swipe">swipe</a></li><li><a href="$.fn.swipe.html#event:swipeDown">swipeDown</a></li><li><a href="$.fn.swipe.html#event:swipeLeft">swipeLeft</a></li><li><a href="$.fn.swipe.html#event:swipeRight">swipeRight</a></li><li><a href="$.fn.swipe.html#event:swipeStatus">swipeStatus</a></li><li><a href="$.fn.swipe.html#event:swipeUp">swipeUp</a></li><li><a href="$.fn.swipe.html#event:tap">tap</a></li></ul><h3>Namespaces</h3><ul><li><a href="$.fn.swipe.defaults.html">defaults</a></li><li><a href="$.fn.swipe.directions.html">directions</a></li><li><a href="$.fn.swipe.fingers.html">fingers</a></li><li><a href="$.fn.swipe.pageScroll.html">pageScroll</a></li><li><a href="$.fn.swipe.phases.html">phases</a></li></ul><h3>Tutorials</h3><ul><li><a href="tutorial-Any_finger_swipe.html">Any finger swipe</a></li><li><a href="tutorial-Basic_swipe.html">Basic swipe</a></li><li><a href="tutorial-Enable_and_destroy.html">Enable and destroy</a></li><li><a href="tutorial-Excluded_children.html">Excluded children</a></li><li><a href="tutorial-Finger_swipe.html">Finger swipe</a></li><li><a href="tutorial-Handlers_and_events.html">Handlers and events</a></li><li><a href="tutorial-Hold.html">Hold</a></li><li><a href="tutorial-Image_gallery_example.html">Image gallery example</a></li><li><a href="tutorial-Options.html">Options</a></li><li><a href="tutorial-Page_scrolling.html">Page scrolling</a></li><li><a href="tutorial-Page_zoom.html">Page zoom</a></li><li><a href="tutorial-Pinch.html">Pinch</a></li><li><a href="tutorial-Pinch_and_Swipe.html">Pinch and Swipe</a></li><li><a href="tutorial-Pinch_status.html">Pinch status</a></li><li><a href="tutorial-Single_swipe.html">Single swipe</a></li><li><a href="tutorial-Stop_propegation.html">Stop propegation</a></li><li><a href="tutorial-Swipe_status.html">Swipe status</a></li><li><a href="tutorial-Tap_vs_swipe.html">Tap vs swipe</a></li><li><a href="tutorial-Thresholds.html">Thresholds</a></li><li><a href="tutorial-Trigger_handlers.html">Trigger handlers</a></li><li><a href="tutorial-index_.html">index</a></li></ul>
</nav>

<div id="main">
    
    <h1 class="page-title">swipe</h1>
    

    




<section>

<header>
    
        <h2>
            <span class="ancestors"><a href="$.html">$</a><a href="$.fn.html">.fn</a>.</span>
        
        swipe
        </h2>
        
    
</header>

<article>
    <div class="container-overview">
    
        

    

    <h4 class="name" id="swipe"><span class="type-signature"></span>new swipe<span class="signature">(method)</span><span class="type-signature"></span></h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line290">line 290</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li>TouchSwipe</li>
        </ul>
    </dd>
    

    
</dl>





<div class="description">
    Applies TouchSwipe behaviour to one or more jQuery objects.
The TouchSwipe plugin can be instantiated via this method, or methods within
TouchSwipe can be executed via this method as per jQuery plugin architecture.
An existing plugin can have its options changed simply by re calling .swipe(options)
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>method</code></td>
            

            <td class="type">
            
                
<span class="param-type">Mixed</span>


            
            </td>

            

            

            <td class="description last">If the current DOMNode is a TouchSwipe object, and <code>method</code> is a TouchSwipe method, then
the <code>method</code> is executed, and any following arguments are passed to the TouchSwipe method.
If <code>method</code> is an object, then the TouchSwipe class is instantiated on the current DOMNode, passing the
configuration properties defined in the object. See TouchSwipe</td>
        </tr>

    
    </tbody>
</table>
















    
    </div>

    

    

    

     

    
        <h3 class="subsection-title">Namespaces</h3>

        <dl>
            <dt><a href="$.fn.swipe.defaults.html">defaults</a></dt>
            <dd></dd>
        
            <dt><a href="$.fn.swipe.directions.html">directions</a></dt>
            <dd></dd>
        
            <dt><a href="$.fn.swipe.fingers.html">fingers</a></dt>
            <dd></dd>
        
            <dt><a href="$.fn.swipe.pageScroll.html">pageScroll</a></dt>
            <dd></dd>
        
            <dt><a href="$.fn.swipe.phases.html">phases</a></dt>
            <dd></dd>
        </dl>
    

    
        <h3 class="subsection-title">Members</h3>

        
            
<h4 class="name" id=".version"><span class="type-signature">(static, readonly) </span>version<span class="type-signature"></span></h4>





<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line320">line 320</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>





<div class="description">
    The version of the plugin
</div>








        
    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    <h4 class="name" id="destroy"><span class="type-signature"></span>destroy<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line546">line 546</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>





<div class="description">
    Destroy the swipe plugin completely. To use any swipe methods, you must re initialise the plugin.
</div>









    <h5>Example</h5>
    
    <pre class="prettyprint"><code>$("#element").swipe("destroy");</code></pre>


















        
            

    

    <h4 class="name" id="disable"><span class="type-signature"></span>disable<span class="signature">()</span><span class="type-signature"> &rarr; {DOMNode}</span></h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line534">line 534</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>





<div class="description">
    disables the swipe plugin
</div>









    <h5>Example</h5>
    
    <pre class="prettyprint"><code>$("#element").swipe("disable");</code></pre>
















<h5>Returns:</h5>

        
<div class="param-desc">
    The Dom element that is now registered with TouchSwipe
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">DOMNode</span>


    </dd>
</dl>

    


        
            

    

    <h4 class="name" id="enable"><span class="type-signature"></span>enable<span class="signature">()</span><span class="type-signature"> &rarr; {DOMNode}</span></h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line519">line 519</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>





<div class="description">
    re-enables the swipe plugin with the previous configuration
</div>









    <h5>Example</h5>
    
    <pre class="prettyprint"><code>$("#element").swipe("enable");</code></pre>
















<h5>Returns:</h5>

        
<div class="param-desc">
    The Dom element that was registered with TouchSwipe
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">DOMNode</span>


    </dd>
</dl>

    


        
            

    

    <h4 class="name" id="option"><span class="type-signature"></span>option<span class="signature">(property, value<span class="signature-attributes">opt</span>)</span><span class="type-signature"> &rarr; {Object}</span></h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line559">line 559</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li><a href="$.fn.swipe.defaults.html">$.fn.swipe.defaults</a></li>
        </ul>
    </dd>
    

    
</dl>





<div class="description">
    Allows run time updating of the swipe configuration options.
</div>









    <h5>Examples</h5>
    
    <pre class="prettyprint"><code>$("#element").swipe("option", "threshold"); // return the threshold</code></pre>

    <pre class="prettyprint"><code>$("#element").swipe("option", "threshold", 100); // set the threshold after init</code></pre>

    <pre class="prettyprint"><code>$("#element").swipe("option", {threshold:100, fingers:3} ); // set multiple properties after init</code></pre>

    <pre class="prettyprint"><code>$("#element").swipe({threshold:100, fingers:3} ); // set multiple properties after init - the "option" method is optional!</code></pre>

    <pre class="prettyprint"><code>$("#element").swipe("option"); // Return the current options hash</code></pre>




    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>property</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last">The option property to get or set, or a has of multiple options to set</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">The value to set the property to</td>
        </tr>

    
    </tbody>
</table>














<h5>Returns:</h5>

        
<div class="param-desc">
    If only a property name is passed, then that property value is returned. If nothing is passed the current options hash is returned.
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>


    </dd>
</dl>

    


        
    

    

    
        <h3 class="subsection-title">Events</h3>

        
            

    

    <h4 class="name" id="event:click">click</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line2052">line 2052</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    
        <dt class="important tag-deprecated">Deprecated:</dt><dd><ul class="dummy"><li>since version 1.6.2, please use $.fn.swipe#tap instead</li></ul></dd>
    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A click handler triggered when a user simply clicks, rather than swipes on an element.
This is deprecated since version 1.6.2, any assignment to click will be assigned to the tap handler.
You cannot use <code>on</code> to bind to this event as the default jQ <code>click</code> event will be triggered.
Use the <code>tap</code> event instead.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type">DomObject</span>


            
            </td>

            

            

            <td class="description last">The element clicked on.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:doubleTap">doubleTap</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line2074">line 2074</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li>$.fn.swipe.defaults#doubleTapThreshold</li>
        </ul>
    </dd>
    

    
</dl>





<div class="description">
    A double tap handler triggered when a user double clicks or taps on an element.
You can set the time delay for a double tap with the $.fn.swipe.defaults#doubleTapThreshold property.
Note: If you set both <code>doubleTap</code> and <code>tap</code> handlers, the <code>tap</code> event will be delayed by the <code>doubleTapThreshold</code>
as the script needs to check if its a double tap.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type">DomObject</span>


            
            </td>

            

            

            <td class="description last">The element clicked on.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:hold">hold</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line2098">line 2098</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li>$.fn.swipe.defaults#longTapThreshold</li>
        </ul>
    </dd>
    

    
</dl>





<div class="description">
    A hold tap handler triggered as soon as the longTapThreshold is reached
You can set the time delay for a long tap with the $.fn.swipe.defaults#longTapThreshold property.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type">DomObject</span>


            
            </td>

            

            

            <td class="description last">The element clicked on.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:longTap">longTap</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line2087">line 2087</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li>$.fn.swipe.defaults#longTapThreshold</li>
        </ul>
    </dd>
    

    
</dl>





<div class="description">
    A long tap handler triggered once a tap has been release if the tap was longer than the longTapThreshold.
You can set the time delay for a long tap with the $.fn.swipe.defaults#longTapThreshold property.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type">DomObject</span>


            
            </td>

            

            

            <td class="description last">The element clicked on.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:pinchIn">pinchIn</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line2010">line 2010</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A handler triggered for pinch in events.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The direction the user pinched in. See <a href="$.fn.swipe.directions.html">$.fn.swipe.directions</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>distance</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The distance the user pinched</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The duration of the swipe in milliseconds</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerCount</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The number of fingers used. See <a href="$.fn.swipe.fingers.html">$.fn.swipe.fingers</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>zoom</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The zoom/scale level the user pinched too, 0-1.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerData</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last">The coordinates of fingers in event</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:pinchOut">pinchOut</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line2024">line 2024</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A handler triggered for pinch out events.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The direction the user pinched in. See <a href="$.fn.swipe.directions.html">$.fn.swipe.directions</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>distance</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The distance the user pinched</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The duration of the swipe in milliseconds</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerCount</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The number of fingers used. See <a href="$.fn.swipe.fingers.html">$.fn.swipe.fingers</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>zoom</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The zoom/scale level the user pinched too, 0-1.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerData</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last">The coordinates of fingers in event</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:pinchStatus">pinchStatus</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line2038">line 2038</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A handler triggered for all pinch events. This handler is constantly fired for the duration of the pinch. This is triggered regardless of thresholds.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The direction the user pinched in. See <a href="$.fn.swipe.directions.html">$.fn.swipe.directions</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>distance</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The distance the user pinched</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The duration of the swipe in milliseconds</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerCount</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The number of fingers used. See <a href="$.fn.swipe.fingers.html">$.fn.swipe.fingers</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>zoom</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The zoom/scale level the user pinched too, 0-1.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerData</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last">The coordinates of fingers in event</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:swipe">swipe</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line1921">line 1921</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A catch all handler that is triggered for all swipe directions.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The direction the user swiped in. See <a href="$.fn.swipe.directions.html">$.fn.swipe.directions</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>distance</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The distance the user swiped</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The duration of the swipe in milliseconds</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerCount</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The number of fingers used. See <a href="$.fn.swipe.fingers.html">$.fn.swipe.fingers</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerData</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last">The coordinates of fingers in event</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>currentDirection</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last">The current direction the user is swiping.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:swipeDown">swipeDown</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line1980">line 1980</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A handler that is triggered for "down" swipes.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The direction the user swiped in. See <a href="$.fn.swipe.directions.html">$.fn.swipe.directions</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>distance</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The distance the user swiped</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The duration of the swipe in milliseconds</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerCount</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The number of fingers used. See <a href="$.fn.swipe.fingers.html">$.fn.swipe.fingers</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerData</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last">The coordinates of fingers in event</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>currentDirection</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last">The current direction the user is swiping.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:swipeLeft">swipeLeft</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line1938">line 1938</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A handler that is triggered for "left" swipes.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The direction the user swiped in. See <a href="$.fn.swipe.directions.html">$.fn.swipe.directions</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>distance</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The distance the user swiped</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The duration of the swipe in milliseconds</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerCount</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The number of fingers used. See <a href="$.fn.swipe.fingers.html">$.fn.swipe.fingers</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerData</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last">The coordinates of fingers in event</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>currentDirection</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last">The current direction the user is swiping.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:swipeRight">swipeRight</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line1952">line 1952</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A handler that is triggered for "right" swipes.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The direction the user swiped in. See <a href="$.fn.swipe.directions.html">$.fn.swipe.directions</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>distance</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The distance the user swiped</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The duration of the swipe in milliseconds</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerCount</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The number of fingers used. See <a href="$.fn.swipe.fingers.html">$.fn.swipe.fingers</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerData</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last">The coordinates of fingers in event</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>currentDirection</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last">The current direction the user is swiping.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:swipeStatus">swipeStatus</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line1994">line 1994</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A handler triggered for every phase of the swipe. This handler is constantly fired for the duration of the pinch.
This is triggered regardless of swipe thresholds.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>phase</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last">The phase of the swipe event. See <a href="$.fn.swipe.phases.html">$.fn.swipe.phases</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last">The direction the user swiped in. This is null if the user has yet to move. See <a href="$.fn.swipe.directions.html">$.fn.swipe.directions</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>distance</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The distance the user swiped. This is 0 if the user has yet to move.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The duration of the swipe in milliseconds</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerCount</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The number of fingers used. See <a href="$.fn.swipe.fingers.html">$.fn.swipe.fingers</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerData</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last">The coordinates of fingers in event</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>currentDirection</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last">The current direction the user is swiping.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:swipeUp">swipeUp</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line1966">line 1966</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A handler that is triggered for "up" swipes.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The direction the user swiped in. See <a href="$.fn.swipe.directions.html">$.fn.swipe.directions</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>distance</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The distance the user swiped</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The duration of the swipe in milliseconds</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerCount</code></td>
            

            <td class="type">
            
                
<span class="param-type">int</span>


            
            </td>

            

            

            <td class="description last">The number of fingers used. See <a href="$.fn.swipe.fingers.html">$.fn.swipe.fingers</a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fingerData</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            

            <td class="description last">The coordinates of fingers in event</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>currentDirection</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last">The current direction the user is swiping.</td>
        </tr>

    
    </tbody>
</table>
















        
            

    

    <h4 class="name" id="event:tap">tap</h4>

    




<dl class="details">

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="jquery.touchSwipe.js.html">jquery.touchSwipe.js</a>, <a href="jquery.touchSwipe.js.html#line2065">line 2065</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-default">Default Value:</dt>
    <dd class="tag-default"><ul class="dummy">
            <li>null</li>
        </ul></dd>
    

    

    

    
</dl>





<div class="description">
    A click / tap handler triggered when a user simply clicks or taps, rather than swipes on an element.
</div>











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">EventObject</span>


            
            </td>

            

            

            <td class="description last">The original event object</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type">DomObject</span>


            
            </td>

            

            

            <td class="description last">The element clicked on.</td>
        </tr>

    
    </tbody>
</table>
















        
    
</article>

</section>




</div>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.4.0</a> on Thu May 19 2016 17:22:25 GMT+0100 (BST) using the docdash theme.
</footer>

<script>prettyPrint();</script>
<script src="scripts/linenumber.js"></script>
</body>
</html>