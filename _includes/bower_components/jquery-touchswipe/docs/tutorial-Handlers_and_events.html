<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Tutorial: Handlers and events - Documentation</title>

    <script src="scripts/prettify/prettify.js"></script>
    <script src="scripts/prettify/lang-css.js"></script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <link type="text/css" rel="stylesheet" href="styles/prettify.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
</head>
<body>

<input type="checkbox" id="nav-trigger" class="nav-trigger" />
<label for="nav-trigger" class="navicon-button x">
  <div class="navicon"></div>
</label>

<label for="nav-trigger" class="overlay"></label>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="$.html">$</a></li><li><a href="$.fn.html">fn</a></li><li><a href="$.fn.swipe.html">swipe</a><ul class='methods'><li data-type='method'><a href="$.fn.swipe.html#destroy">destroy</a></li><li data-type='method'><a href="$.fn.swipe.html#disable">disable</a></li><li data-type='method'><a href="$.fn.swipe.html#enable">enable</a></li><li data-type='method'><a href="$.fn.swipe.html#option">option</a></li></ul></li></ul><h3>Events</h3><ul><li><a href="$.fn.swipe.html#event:click">click</a></li><li><a href="$.fn.swipe.html#event:doubleTap">doubleTap</a></li><li><a href="$.fn.swipe.html#event:hold">hold</a></li><li><a href="$.fn.swipe.html#event:longTap">longTap</a></li><li><a href="$.fn.swipe.html#event:pinchIn">pinchIn</a></li><li><a href="$.fn.swipe.html#event:pinchOut">pinchOut</a></li><li><a href="$.fn.swipe.html#event:pinchStatus">pinchStatus</a></li><li><a href="$.fn.swipe.html#event:swipe">swipe</a></li><li><a href="$.fn.swipe.html#event:swipeDown">swipeDown</a></li><li><a href="$.fn.swipe.html#event:swipeLeft">swipeLeft</a></li><li><a href="$.fn.swipe.html#event:swipeRight">swipeRight</a></li><li><a href="$.fn.swipe.html#event:swipeStatus">swipeStatus</a></li><li><a href="$.fn.swipe.html#event:swipeUp">swipeUp</a></li><li><a href="$.fn.swipe.html#event:tap">tap</a></li></ul><h3>Namespaces</h3><ul><li><a href="$.fn.swipe.defaults.html">defaults</a></li><li><a href="$.fn.swipe.directions.html">directions</a></li><li><a href="$.fn.swipe.fingers.html">fingers</a></li><li><a href="$.fn.swipe.pageScroll.html">pageScroll</a></li><li><a href="$.fn.swipe.phases.html">phases</a></li></ul><h3>Tutorials</h3><ul><li><a href="tutorial-Any_finger_swipe.html">Any finger swipe</a></li><li><a href="tutorial-Basic_swipe.html">Basic swipe</a></li><li><a href="tutorial-Enable_and_destroy.html">Enable and destroy</a></li><li><a href="tutorial-Excluded_children.html">Excluded children</a></li><li><a href="tutorial-Finger_swipe.html">Finger swipe</a></li><li><a href="tutorial-Handlers_and_events.html">Handlers and events</a></li><li><a href="tutorial-Hold.html">Hold</a></li><li><a href="tutorial-Image_gallery_example.html">Image gallery example</a></li><li><a href="tutorial-Options.html">Options</a></li><li><a href="tutorial-Page_scrolling.html">Page scrolling</a></li><li><a href="tutorial-Page_zoom.html">Page zoom</a></li><li><a href="tutorial-Pinch.html">Pinch</a></li><li><a href="tutorial-Pinch_and_Swipe.html">Pinch and Swipe</a></li><li><a href="tutorial-Pinch_status.html">Pinch status</a></li><li><a href="tutorial-Single_swipe.html">Single swipe</a></li><li><a href="tutorial-Stop_propegation.html">Stop propegation</a></li><li><a href="tutorial-Swipe_status.html">Swipe status</a></li><li><a href="tutorial-Tap_vs_swipe.html">Tap vs swipe</a></li><li><a href="tutorial-Thresholds.html">Thresholds</a></li><li><a href="tutorial-Trigger_handlers.html">Trigger handlers</a></li><li><a href="tutorial-index_.html">index</a></li></ul>
</nav>

<div id="main">
    
    <h1 class="page-title">Tutorial: Handlers and events</h1>
    

    <section>

<header>
    

    <h2>Handlers and events</h2>
</header>

<article>
    <!DOCTYPE HTML>
<html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta http-equiv="x-ua-compatible" content="IE=9">
        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />

        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>

        <!-- use the jquery.ui.ipad.js plugin to translate touch events to mouse events -->
		<script type="text/javascript" src="js/jquery.ui.ipad.js"></script>

        <title>touchSwipe</title>
    </head>
    <body>
		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">

			<script id='code_1'>
				$(function() {
					$("#register_btn").click(function() {
                        if($(this).text() == "Register Events") {
			                registerEvents();
                        } else {
			                deRegisterEvents();
			            }
			        });

					// Create the swipe object, and assign the callbacks
					$("#test").swipe( {
						tap:function(event, target) {
                            log("tap from callback");
						},
						hold:function(event, target) {
                            log("hold from callback");
						},
						swipe:function(event, direction, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipe from callback");
						},
						swipeLeft:function(event, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipeLeft from callback");
						},
						swipeRight:function(event, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipeRight from callback");
						},
						swipeUp:function(event, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipeUp from callback");
						},
						swipeDown:function(event, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipeDown from callback");
						},
						swipeStatus:function(event, phase, direction, distance, duration, fingers, fingerData, currentDirection) {
    						log("swipeStatus from callback");
						},
						pinchIn:function(event, direction, distance, duration, fingerCount, pinchZoom, fingerData) {
							log("pinchIn from callback");
						},
						pinchOut:function(event, direction, distance, duration, fingerCount, pinchZoom, fingerData) {
                            log("pinchOut from callback");
						},
						pinchStatus:function(event, phase, direction, distance , duration , fingerCount, pinchZoom, fingerData) {
                            log("pinchStatus from callback");
						},

                        fingers:$.fn.swipe.fingers.ALL
					});

					//Now assign the event handlers as well
					var events = ['tap','hold', 'swipe','swipeLeft','swipeRight','swipeUp','swipeDown','swipeStatus','pinch','pinchIn','pinchOut','pinchStatus'];

					function registerEvents() {
					    for(var i in events) {
					        $("#test").on( events[i], logEvent);
					    }
					     $("#register_btn").text("Remove Events").removeClass('btn-success').addClass('btn-danger');
					}

					function deRegisterEvents() {
					    for(var i in events) {
					        $("#test").off( events[i], logEvent);
					    }
					     $("#register_btn").text("Register Events").removeClass('btn-danger').addClass('btn-success');
					}

					function logEvent(event) {
					    log("<b>" + event.type + " from event handler</b>");
					}

					function log(msg) {
					    $("#test").html( msg + "<br />" + $("#test").html() );
					}

					registerEvents();
				});
			</script>

			<script>
			    $(function() {

    			});
			</script>

			<span class='title'></span>
			<h4>events:  <span class='events'><code>tap</code> <code>hold</code> <code>swipe</code> <code>swipeLeft</code> <code>swipeRight</code> <code>swipeUp</code> <code>swipeDown</code> <code>swipeStatus</code> <code>pinch</code> <code>pinchIn</code> <code>pinchOut</code> <code>pinchStatus</code></span> </h4>
			<b>See the <a href="../docs/%24.fn.swipe.html#event:click"><u>docs</u></a> for more on each event</b>
			<p>You can either assign callback methods as part of the options object, or you can assign
			event handlers using the jQuery <code>on</code>/<code>off</code> event registration.</p>

			<p>The example below logs both from the callback and the event handlers. The Remove Events button will remove the
			event bindings, and then only the callbacks will be logged. Event logs are bold, callbacks are normal.</p>

			<button class='btn btn-small btn-info example_btn'>Jump to Example</button>
			<pre class="prettyprint lang-js" data-src='code_1'></pre>
			<span class='navigation'></span>

			<br/>
			<button class='btn btn-danger' id="register_btn"></button>
			<div id="test" class="box" style="font-size:10px;"></div>

			<span class='navigation'></span>
		</div>
   </body>
</html>

</article>

</section>

</div>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.4.0</a> on Thu May 19 2016 17:22:25 GMT+0100 (BST) using the docdash theme.
</footer>

<script>prettyPrint();</script>
<script src="scripts/linenumber.js"></script>
</body>
</html>