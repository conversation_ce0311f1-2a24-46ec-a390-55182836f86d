<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Home - Documentation</title>

    <script src="scripts/prettify/prettify.js"></script>
    <script src="scripts/prettify/lang-css.js"></script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <link type="text/css" rel="stylesheet" href="styles/prettify.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
</head>
<body>

<input type="checkbox" id="nav-trigger" class="nav-trigger" />
<label for="nav-trigger" class="navicon-button x">
  <div class="navicon"></div>
</label>

<label for="nav-trigger" class="overlay"></label>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="$.html">$</a></li><li><a href="$.fn.html">fn</a></li><li><a href="$.fn.swipe.html">swipe</a><ul class='methods'><li data-type='method'><a href="$.fn.swipe.html#destroy">destroy</a></li><li data-type='method'><a href="$.fn.swipe.html#disable">disable</a></li><li data-type='method'><a href="$.fn.swipe.html#enable">enable</a></li><li data-type='method'><a href="$.fn.swipe.html#option">option</a></li></ul></li></ul><h3>Events</h3><ul><li><a href="$.fn.swipe.html#event:click">click</a></li><li><a href="$.fn.swipe.html#event:doubleTap">doubleTap</a></li><li><a href="$.fn.swipe.html#event:hold">hold</a></li><li><a href="$.fn.swipe.html#event:longTap">longTap</a></li><li><a href="$.fn.swipe.html#event:pinchIn">pinchIn</a></li><li><a href="$.fn.swipe.html#event:pinchOut">pinchOut</a></li><li><a href="$.fn.swipe.html#event:pinchStatus">pinchStatus</a></li><li><a href="$.fn.swipe.html#event:swipe">swipe</a></li><li><a href="$.fn.swipe.html#event:swipeDown">swipeDown</a></li><li><a href="$.fn.swipe.html#event:swipeLeft">swipeLeft</a></li><li><a href="$.fn.swipe.html#event:swipeRight">swipeRight</a></li><li><a href="$.fn.swipe.html#event:swipeStatus">swipeStatus</a></li><li><a href="$.fn.swipe.html#event:swipeUp">swipeUp</a></li><li><a href="$.fn.swipe.html#event:tap">tap</a></li></ul><h3>Namespaces</h3><ul><li><a href="$.fn.swipe.defaults.html">defaults</a></li><li><a href="$.fn.swipe.directions.html">directions</a></li><li><a href="$.fn.swipe.fingers.html">fingers</a></li><li><a href="$.fn.swipe.pageScroll.html">pageScroll</a></li><li><a href="$.fn.swipe.phases.html">phases</a></li></ul><h3>Tutorials</h3><ul><li><a href="tutorial-Any_finger_swipe.html">Any finger swipe</a></li><li><a href="tutorial-Basic_swipe.html">Basic swipe</a></li><li><a href="tutorial-Enable_and_destroy.html">Enable and destroy</a></li><li><a href="tutorial-Excluded_children.html">Excluded children</a></li><li><a href="tutorial-Finger_swipe.html">Finger swipe</a></li><li><a href="tutorial-Handlers_and_events.html">Handlers and events</a></li><li><a href="tutorial-Hold.html">Hold</a></li><li><a href="tutorial-Image_gallery_example.html">Image gallery example</a></li><li><a href="tutorial-Options.html">Options</a></li><li><a href="tutorial-Page_scrolling.html">Page scrolling</a></li><li><a href="tutorial-Page_zoom.html">Page zoom</a></li><li><a href="tutorial-Pinch.html">Pinch</a></li><li><a href="tutorial-Pinch_and_Swipe.html">Pinch and Swipe</a></li><li><a href="tutorial-Pinch_status.html">Pinch status</a></li><li><a href="tutorial-Single_swipe.html">Single swipe</a></li><li><a href="tutorial-Stop_propegation.html">Stop propegation</a></li><li><a href="tutorial-Swipe_status.html">Swipe status</a></li><li><a href="tutorial-Tap_vs_swipe.html">Tap vs swipe</a></li><li><a href="tutorial-Thresholds.html">Thresholds</a></li><li><a href="tutorial-Trigger_handlers.html">Trigger handlers</a></li><li><a href="tutorial-index_.html">index</a></li></ul>
</nav>

<div id="main">
    

    



    









    


    <section class="readme">
        <article><h1>TouchSwipe 1.6</h1><p>A jQuery plugin to be used on touch devices such as iPad, iPhone, Android etc.</p>
<p>Detects single and multiple finger swipes, pinches and falls back to mouse 'drags' on the desktop.</p>
<p>Time and distance thresholds can be set to distinguish between swipe gesture and slow drag.</p>
<p>Allows exclusion of child elements (interactive elements) as well allowing page scrolling or page zooming depending on configuration.</p>
<ul>
<li>Detects swipes in 4 directions, &quot;up&quot;, &quot;down&quot;, &quot;left&quot; and &quot;right&quot;</li>
<li>Detects pinches &quot;in&quot; and &quot;out&quot;</li>
<li>Supports single finger or double finger touch events</li>
<li>Supports click events both on the touchSwipe object and its child objects</li>
<li>Definable threshold / maxTimeThreshold to determin when a gesture is actually a swipe</li>
<li>Events triggered for swipe &quot;start&quot;,&quot;move&quot;,&quot;end&quot; and &quot;cancel&quot;</li>
<li>End event can be triggered either on touch release, or as soon as threshold is met</li>
<li>Allows swiping and page scrolling</li>
<li>Disables user input elements (Button, form, text etc) from triggering swipes</li>
</ul>
<h2>Demos, examples and docs</h2><p><a href="http://labs.rampinteractive.co.uk/touchSwipe">http://labs.rampinteractive.co.uk/touchSwipe</a><br><a href="http://labs.rampinteractive.co.uk/touchSwipe/docs">http://labs.rampinteractive.co.uk/touchSwipe/docs</a></p>
<h2>Installation</h2><h3>NPM</h3><pre class="prettyprint source lang-bash"><code>npm install jquery-touchswipe --save</code></pre><h3>Bower</h3><pre class="prettyprint source lang-bash"><code>bower install jquery-touchswipe --save</code></pre><h3>Manual</h3><p>Include the minified file in your project.</p>
<pre class="prettyprint source lang-html"><code>&lt;script type=&quot;text/javascript&quot; src=&quot;js/jquery.touchSwipe.min.js&quot;>&lt;/script></code></pre><h2>Usage</h2><pre class="prettyprint source lang-javascript"><code>$(function() {
  $(&quot;#test&quot;).swipe( {
    //Generic swipe handler for all directions
    swipe:function(event, direction, distance, duration, fingerCount, fingerData) {
      $(this).text(&quot;You swiped &quot; + direction );  
    }
  });

  //Set some options later
  $(&quot;#test&quot;).swipe( {fingers:2} );
});</code></pre><p>For full demos, code examples and documentation, see below.</p>
<h2>Development</h2><p>Install dependencies</p>
<pre class="prettyprint source lang-bash"><code>npm install</code></pre><p>To minify</p>
<pre class="prettyprint source lang-bash"><code>npm run minify</code></pre><p>To build docs</p>
<pre class="prettyprint source lang-bash"><code>npm run docs</code></pre><p>To do both</p>
<pre class="prettyprint source lang-bash"><code>npm run build</code></pre><h3>For port to XUI see:</h3><p>https://github.com/cowgp/xui-touchSwipe</p></article>
    </section>






</div>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.4.0</a> on Thu May 19 2016 17:22:25 GMT+0100 (BST) using the docdash theme.
</footer>

<script>prettyPrint();</script>
<script src="scripts/linenumber.js"></script>
</body>
</html>