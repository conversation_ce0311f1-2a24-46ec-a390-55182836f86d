<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Tutorial: index - Documentation</title>

    <script src="scripts/prettify/prettify.js"></script>
    <script src="scripts/prettify/lang-css.js"></script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <link type="text/css" rel="stylesheet" href="styles/prettify.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
</head>
<body>

<input type="checkbox" id="nav-trigger" class="nav-trigger" />
<label for="nav-trigger" class="navicon-button x">
  <div class="navicon"></div>
</label>

<label for="nav-trigger" class="overlay"></label>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="$.html">$</a></li><li><a href="$.fn.html">fn</a></li><li><a href="$.fn.swipe.html">swipe</a><ul class='methods'><li data-type='method'><a href="$.fn.swipe.html#destroy">destroy</a></li><li data-type='method'><a href="$.fn.swipe.html#disable">disable</a></li><li data-type='method'><a href="$.fn.swipe.html#enable">enable</a></li><li data-type='method'><a href="$.fn.swipe.html#option">option</a></li></ul></li></ul><h3>Events</h3><ul><li><a href="$.fn.swipe.html#event:click">click</a></li><li><a href="$.fn.swipe.html#event:doubleTap">doubleTap</a></li><li><a href="$.fn.swipe.html#event:hold">hold</a></li><li><a href="$.fn.swipe.html#event:longTap">longTap</a></li><li><a href="$.fn.swipe.html#event:pinchIn">pinchIn</a></li><li><a href="$.fn.swipe.html#event:pinchOut">pinchOut</a></li><li><a href="$.fn.swipe.html#event:pinchStatus">pinchStatus</a></li><li><a href="$.fn.swipe.html#event:swipe">swipe</a></li><li><a href="$.fn.swipe.html#event:swipeDown">swipeDown</a></li><li><a href="$.fn.swipe.html#event:swipeLeft">swipeLeft</a></li><li><a href="$.fn.swipe.html#event:swipeRight">swipeRight</a></li><li><a href="$.fn.swipe.html#event:swipeStatus">swipeStatus</a></li><li><a href="$.fn.swipe.html#event:swipeUp">swipeUp</a></li><li><a href="$.fn.swipe.html#event:tap">tap</a></li></ul><h3>Namespaces</h3><ul><li><a href="$.fn.swipe.defaults.html">defaults</a></li><li><a href="$.fn.swipe.directions.html">directions</a></li><li><a href="$.fn.swipe.fingers.html">fingers</a></li><li><a href="$.fn.swipe.pageScroll.html">pageScroll</a></li><li><a href="$.fn.swipe.phases.html">phases</a></li></ul><h3>Tutorials</h3><ul><li><a href="tutorial-Any_finger_swipe.html">Any finger swipe</a></li><li><a href="tutorial-Basic_swipe.html">Basic swipe</a></li><li><a href="tutorial-Enable_and_destroy.html">Enable and destroy</a></li><li><a href="tutorial-Excluded_children.html">Excluded children</a></li><li><a href="tutorial-Finger_swipe.html">Finger swipe</a></li><li><a href="tutorial-Handlers_and_events.html">Handlers and events</a></li><li><a href="tutorial-Hold.html">Hold</a></li><li><a href="tutorial-Image_gallery_example.html">Image gallery example</a></li><li><a href="tutorial-Options.html">Options</a></li><li><a href="tutorial-Page_scrolling.html">Page scrolling</a></li><li><a href="tutorial-Page_zoom.html">Page zoom</a></li><li><a href="tutorial-Pinch.html">Pinch</a></li><li><a href="tutorial-Pinch_and_Swipe.html">Pinch and Swipe</a></li><li><a href="tutorial-Pinch_status.html">Pinch status</a></li><li><a href="tutorial-Single_swipe.html">Single swipe</a></li><li><a href="tutorial-Stop_propegation.html">Stop propegation</a></li><li><a href="tutorial-Swipe_status.html">Swipe status</a></li><li><a href="tutorial-Tap_vs_swipe.html">Tap vs swipe</a></li><li><a href="tutorial-Thresholds.html">Thresholds</a></li><li><a href="tutorial-Trigger_handlers.html">Trigger handlers</a></li><li><a href="tutorial-index_.html">index</a></li></ul>
</nav>

<div id="main">
    
    <h1 class="page-title">Tutorial: index</h1>
    

    <section>

<header>
    

    <h2>index</h2>
</header>

<article>
    <!DOCTYPE HTML>
<html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>

        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />

        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>

        <title>touchSwipe</title>

		<script type="text/javascript">
			//Google analyitics
			 var _gaq = _gaq || [];
			  _gaq.push(['_setAccount', 'UA-********-3']);
			  _gaq.push(['_trackPageview']);

			  (function() {
				var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
				ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
				var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
			  })();

		</script>

	</head>
	<body>

		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">
			<div class="page-header">
			  <h1>TouchSwipe <small>a jQuery plugin for touch devices</small></h1>
			  <br/>
			  <iframe src="http://www.facebook.com/plugins/like.php?href=http%3A%2F%2Flabs.skinkers.com%2FtouchSwipe%2F&amp;layout=standard&amp;show_faces=true&amp;width=450&amp;action=like&amp;font&amp;colorscheme=light&amp;height=80" scrolling="no" frameborder="0" style="border:none; overflow:hidden; width:450px; height:80px;" allowTransparency="true"></iframe>
				<p>TouchSwipe is a jquery plugin to be used with jQuery on touch input devices such as iPad, iPhone etc.</p>
			</div>

			<h1>Features</h1>
			<ul>
				<li>Detects swipes in 4 directions, "up", "down", "left" and "right"</li>
				<li>Detects pinches "in" and "out"</li>
				<li>Supports single finger or double finger touch events</li>
				<li>Supports click events both on the touchSwipe object and its child objects</li>
				<li>Definable threshold / maxTimeThreshold to determin when a gesture is actually a swipe</li>
				<li>Events triggered for swipe "start","move","end" and "cancel"</li>
				<li>End event can be triggered either on touch release, or as soon as threshold is met</li>
				<li>Allows swiping and page scrolling</li>
				<li>Disables user input elements (Button, form, text etc) from triggering swipes</li>

			</ul>

			<h1>Docs</h1>
			<ul>
				<li><a href='../docs' target='docs'>Plugin Documentation</a></li>
			</ul>

			<h1>Options</h1>
			<ul>
				<li>See the defaults page in the documentation:  			<a href='../docs/%24.fn.swipe.defaults.html' target='docs'>$.fn.swipe.defaults</a></li>
			</ul>

			<h1>Demos</h1>
			<span class='navigation_list'></span>


		</div>
		<hr/>
	</body>
</html>

</article>

</section>

</div>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.4.0</a> on Thu May 19 2016 17:22:25 GMT+0100 (BST) using the docdash theme.
</footer>

<script>prettyPrint();</script>
<script src="scripts/linenumber.js"></script>
</body>
</html>