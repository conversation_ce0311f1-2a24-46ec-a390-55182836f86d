<!DOCTYPE HTML>
<html>
    <head>  
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta http-equiv="x-ua-compatible" content="IE=9">
        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />
        
        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
        
        <!-- use the jquery.ui.ipad.js plugin to translate touch events to mouse events -->
		<script type="text/javascript" src="js/jquery.ui.ipad.js"></script>
		
        <title>touchSwipe</title>
    </head>
    <body>
		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">
			
			<script id='code_1'>
				$(function() {	
					//Enable swiping...
					$("#test").swipe( {
						hold:function(event, target) {
							
							$("#textText").html("You held the tap until the longTapthreshold was reached" );
						},
						
						threshold:50
					});
					
					
				});
			</script>
			
			<span class='title'></span>
			<h4>events:  <span class='events'><code>tap</code>, <code>hold</code></span></h4>
			<h4>properties:  <span class='properties'><code>longTapThreshold</code></span></h4>
			<p>You can also detect if the user holds their finger down until the longTapthreshold is met<br/>
				The <code>hold</code> handler is passed the original event object and the target that was clicked.
				<br/></p>
				<p class="muted">If you use the jquery.ui.ipad.js plugin (http://code.google.com/p/jquery-ui-for-ipad-and-iphone/) you can then also pickup
				standard jQuery mouse events on children of the touchSwipe object.</p>
				
				
			<button class='btn btn-small btn-info example_btn'>Jump to Example</button>
			<pre class="prettyprint lang-js" data-src='code_1'></pre>
			<span class='navigation'></span>
			
			<div id="test" class="box">
				<div id="textText">Hold me :)</div><br/>
			</div>
			
			<span class='navigation'></span>
		</div>
   </body>
</html>
