<!DOCTYPE HTML>
<html>
    <head>  
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta http-equiv="x-ua-compatible" content="IE=9">
        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />
        
        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
        
        <title>touchSwipe</title>
    </head>
    <body>
		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">
			
			<script id='code_1'>
				$(function() {			
					//Enable swiping...
					$("#test").swipe( {
						swipeStatus:function(event, phase, direction, distance, fingerCount) {
							var str = "";

							switch (phase) {
								case "start" : str="Started"; break;
								case "move" : str="You have moved " + distance +" pixels, past 200 and the handler will fire"; break;
								case "end" : str="Handler fired, you swiped " + direction; break;
								case "cancel" : str="cancel handler fired"; break;
							}
							
							$(this).text(str);

							//This will cancel the current swipe and immediately re run this handler with a cancel event
							return false;
						}
					});
				});
			</script>
	
			<span class='title'></span>
			<h4>events:  <span class='events'><code>swipeStatus</code></span></h4>
			<p>In your event handlers, you can return a value of false if you want to manually cancel the swipe. This will trigger the 'cancel' event.</p>
			
			<button class='btn btn-small btn-info example_btn'>Jump to Example</button>
			<pre class="prettyprint lang-js" data-src="code_1"></pre>
			<span class='navigation'></span>
			
			<div id="test" class="box">Swipe will start, but then cancel as the event handler returns false</div>
			
			<span class='navigation'></span>
		</div>
   </body>
</html>
