<!DOCTYPE HTML>
<html>
    <head>  
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta http-equiv="x-ua-compatible" content="IE=9">
        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />
        
        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
        
        <title>touchSwipe</title>
    </head>
    <body>
		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">
			
			<script id='code_1'>
				
				//Assign handlers to the simple direction handlers.
				var swipeOptions=
				{
					swipe:swipe,
					threshold:0
				}
				
				$(function()
				{	
					$("#disable").click(function(){
						if($("#test").swipe("disable"))
							$("#test").text("Swipe me and nothing happens");
					});
					
					$("#enable").click(function(){
						if($("#test").swipe("enable"))
							$("#test").text("Now I can be Swiped again");
					});
				
					$("#destroy").click(function(){
						if($("#test").swipe("destroy"))
							$("#test").text("I am no longer a touch swipe element");
					});
				
					$("#init").click(function(){
						if( $("#test").swipe( swipeOptions ) )
						{
							$("#test").data('count', 0);
							$("#test").text("I am now a touch swipe element");
						}
					});
					
					$("#test").swipe( swipeOptions );
					$("#test2").swipe( swipeOptions );
					
				});
			
				//Swipe handlers.
				//The only arg passed is the original touch event object	
				function swipe(event, direction)
				{
					var count = this.data('count') ? this.data('count') + 1 : 1;
					this.data('count', count);
					this.text("You swiped " + direction + ". You swiped " + count + " times");
				}
			</script>
			
			<span class='title'></span>
			<h4>methods: <span class="methods"><code>enable</code>, <code>disable</code>, <code>destroy</code></span></h4>
			<p>By using the <code>enable</code>,  <code>disable</code> and <code>destroy</code> methods, you can temporarily disable interaction with a swipe element, or completely destroy it, which requires re instantiation.</br>
			</p>
			
			<span class='navigation'></span>
			
			</br>
			
			<div class="btn-group">
				<button class='btn' id="disable">disable</button>
				<button class='btn' id="enable">enable</button>
				<button class='btn'id="destroy">destroy</button>
				<button class='btn' id="init">init</button>
			</div>
			
			<div id="test" class="box">Swipe me</div>
			
			<br/><br/>
			
			<div id="test2" class="box">Swipe me - Im not affected by the above...</div>
			
			<span class='navigation'></span>
		</div>
   </body>
</html>
