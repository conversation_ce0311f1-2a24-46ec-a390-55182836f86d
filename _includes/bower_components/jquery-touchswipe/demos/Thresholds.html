<!DOCTYPE HTML>
<html>
    <head>  
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta http-equiv="x-ua-compatible" content="IE=9">
        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />
        
        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
        
        <title>touchSwipe</title>
    </head>
    <body>
		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">
			
			<script id='code_1'>
				$(function() {			
					$("#test").swipe( {
						swipe:function(event, direction) {
							$(this).text("You swiped " + direction);
						},
						swipeStatus:function(event, phase) {
							if (phase=="cancel") {
								$(this).text("You didnt swipe far enough ");
							}
						},
						threshold:200
					 });
				});
			</script>
				
			<script id='code_2'>		
				$(function() {				
					$("#test2").swipe( {
						swipe:function(event, direction) {
							$(this).text("You swiped " + direction );
						},
						swipeStatus:function(event, phase) {
							if (phase=="cancel") {
								$("#test2").text("Your swipe was too slow ");
							}
						},
						maxTimeThreshold:1000,
						threshold:null,
						triggerOnTouchEnd:false
					});
				});
			</script>
			
			
			<script id='code_3'>		
				$(function() {				
					$("#test3").swipe( {
						swipe:function(event, direction) {
							$(this).text("You swiped " + direction );
						},
						swipeStatus:function(event, phase, direction, distance, duration, fingers) {
							
							$("#test3").text(" Your have swiped " + distance + " px so far");
							
							if(distance>200) {
							    $("#test3").text(" Now swipe back 10px and release to cancel.. distance = " + distance + "px");
							}
							
							if (phase=="cancel") {
								$("#test3").text(" You cancelled the swipe");
							}
						},
						threshold:200,
						cancelThreshold:10
						
					});
				});
			</script>
			
			<span class='title'></span>
			<h4>property: <span class='properties'><code>threshold</code></span></h4>
			<p>By setting the <code>threshold</code> you can set how far the user must swipe before it is considered a swipe. <br/>Swipe at least 200px</p>
			
			<button class='btn btn-small btn-info example_btn'>Jump to Example</button>
			<pre class="prettyprint lang-js" data-src="code_1"></pre>
			<span class='navigation'></span>
			
			<div id="test" class="box">Swipe me for at least 200 px</div>
			
			<h4>property: <span class='properties'><code>cancelThreshold</code></span><h4>
			<p>By setting the <code>cancelThreshold</code> you can set the minimum distance in px that the user needs to swipe back to cancel the current swipe, even if they have passed the main <code>threshold</code></p>
			

			<pre class="prettyprint lang-js" data-src="code_3"></pre>			
			<div id="test3" class='box'>Swipe me for at least 200 px, then back 10px to cancel</div>
			
			<h4>property: <span class='properties'><code>maxTimeThreshold</code></span><h4>
			<p>By setting the <code>maxTimeThreshold</code> you can set the maximum time the user has to complete the swipe. A swipe LONGER than this is cancelled. This can be useful for ignoring long slow swipes. <br/>Swipe in under 500ms</p>
			

			<pre class="prettyprint lang-js" data-src="code_2"></pre>			
			<div id="test2" class='box'>Swipe me within 1000 ms</div>
			

			<span class='navigation'></span>
		</div>
   </body>
</html>
