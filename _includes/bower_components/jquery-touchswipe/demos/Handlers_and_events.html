<!DOCTYPE HTML>
<html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta http-equiv="x-ua-compatible" content="IE=9">
        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />

        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>

        <!-- use the jquery.ui.ipad.js plugin to translate touch events to mouse events -->
		<script type="text/javascript" src="js/jquery.ui.ipad.js"></script>

        <title>touchSwipe</title>
    </head>
    <body>
		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">

			<script id='code_1'>
				$(function() {
					$("#register_btn").click(function() {
                        if($(this).text() == "Register Events") {
			                registerEvents();
                        } else {
			                deRegisterEvents();
			            }
			        });

					// Create the swipe object, and assign the callbacks
					$("#test").swipe( {
						tap:function(event, target) {
                            log("tap from callback");
						},
						hold:function(event, target) {
                            log("hold from callback");
						},
						swipe:function(event, direction, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipe from callback");
						},
						swipeLeft:function(event, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipeLeft from callback");
						},
						swipeRight:function(event, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipeRight from callback");
						},
						swipeUp:function(event, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipeUp from callback");
						},
						swipeDown:function(event, distance, duration, fingerCount, fingerData, currentDirection) {
							log("swipeDown from callback");
						},
						swipeStatus:function(event, phase, direction, distance, duration, fingers, fingerData, currentDirection) {
    						log("swipeStatus from callback");
						},
						pinchIn:function(event, direction, distance, duration, fingerCount, pinchZoom, fingerData) {
							log("pinchIn from callback");
						},
						pinchOut:function(event, direction, distance, duration, fingerCount, pinchZoom, fingerData) {
                            log("pinchOut from callback");
						},
						pinchStatus:function(event, phase, direction, distance , duration , fingerCount, pinchZoom, fingerData) {
                            log("pinchStatus from callback");
						},

                        fingers:$.fn.swipe.fingers.ALL
					});

					//Now assign the event handlers as well
					var events = ['tap','hold', 'swipe','swipeLeft','swipeRight','swipeUp','swipeDown','swipeStatus','pinch','pinchIn','pinchOut','pinchStatus'];

					function registerEvents() {
					    for(var i in events) {
					        $("#test").on( events[i], logEvent);
					    }
					     $("#register_btn").text("Remove Events").removeClass('btn-success').addClass('btn-danger');
					}

					function deRegisterEvents() {
					    for(var i in events) {
					        $("#test").off( events[i], logEvent);
					    }
					     $("#register_btn").text("Register Events").removeClass('btn-danger').addClass('btn-success');
					}

					function logEvent(event) {
					    log("<b>" + event.type + " from event handler</b>");
					}

					function log(msg) {
					    $("#test").html( msg + "<br />" + $("#test").html() );
					}

					registerEvents();
				});
			</script>

			<script>
			    $(function() {

    			});
			</script>

			<span class='title'></span>
			<h4>events:  <span class='events'><code>tap</code> <code>hold</code> <code>swipe</code> <code>swipeLeft</code> <code>swipeRight</code> <code>swipeUp</code> <code>swipeDown</code> <code>swipeStatus</code> <code>pinch</code> <code>pinchIn</code> <code>pinchOut</code> <code>pinchStatus</code></span> </h4>
			<b>See the <a href="../docs/%24.fn.swipe.html#event:click"><u>docs</u></a> for more on each event</b>
			<p>You can either assign callback methods as part of the options object, or you can assign
			event handlers using the jQuery <code>on</code>/<code>off</code> event registration.</p>

			<p>The example below logs both from the callback and the event handlers. The Remove Events button will remove the
			event bindings, and then only the callbacks will be logged. Event logs are bold, callbacks are normal.</p>

			<button class='btn btn-small btn-info example_btn'>Jump to Example</button>
			<pre class="prettyprint lang-js" data-src='code_1'></pre>
			<span class='navigation'></span>

			<br/>
			<button class='btn btn-danger' id="register_btn"></button>
			<div id="test" class="box" style="font-size:10px;"></div>

			<span class='navigation'></span>
		</div>
   </body>
</html>
