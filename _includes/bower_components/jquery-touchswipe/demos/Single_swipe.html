<!DOCTYPE HTML>
<html>
    <head>  
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta http-equiv="x-ua-compatible" content="IE=9">
        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />
        
        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
        
        <title>touchSwipe</title>
    </head>
    <body>
		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">
			
			<script id='code_1'>
				$(function() {			
					//Keep track of how many swipes
					var count=0;

					//Enable swiping...
					$("#test").swipe( {
						//Single swipe handler for left swipes
						swipeLeft:function(event, direction, distance, duration, fingerCount) {
							$(this).text("You swiped " + direction + " " + ++count + " times " );	
						},
						//Default is 75px, set to 0 for demo so any distance triggers swipe
						threshold:0
					});
				});
			</script>
			
			
			<span class='title'></span>
			<h4>events:  <span class='events'><code>swipeLeft</code>, <code>swipeRight</code>, <code>swipeUp</code>, <code>swipeDown</code>, <code>swipe</code></span></h4>
			<p>By using just one handler <code>swipeLeft</code> you can detect ONLY left swipes. There are handlers for each direction, as well as the generic <code>swipe</code> handler.</p>
			
			<button class='btn btn-small btn-info example_btn'>Jump to Example</button>
			<pre class="prettyprint lang-js" data-src='code_1'></pre>
			<span class='navigation'></span>
			<div id="test" class="box">I only swipe left</div>
	
			<span class='navigation'></span>
		</div>		
   </body>
</html>
