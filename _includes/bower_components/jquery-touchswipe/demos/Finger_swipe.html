<!DOCTYPE HTML>
<html>
    <head>  
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta http-equiv="x-ua-compatible" content="IE=9">
        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />
        
        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
        
        <title>touchSwipe</title>
    </head>
    <body>
		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">
			
			<script id='code_1'>
				$(function() {
					$("#test, #test_2").swipe( {
						swipeStatus:function(event, phase, direction, distance, duration, fingers, fingerData) {

						if(phase==$.fn.swipe.phases.PHASE_START) {
							$(this).text("moving...");
						} 

						if(phase==$.fn.swipe.phases.PHASE_CANCEL) {
							$(this).text("swipe cancelled (due to finger count) "  );
						}   
					  },
					  swipe:function(event, direction, distance, duration, fingerCount, fingerData) {
					  	$(this).text("You swiped " + direction + " with " + fingerCount + " fingers");
					  },
					  threshold:0,
					  fingers:2
					});

                    $("#test_2").swipe({ fingers:3 } );
				});
			</script>
			
			
			<span class='title'></span>
			<h4>property:  <span class='properties'><code>fingers</code></span></h4>
			<p>By setting the number of <code>fingers</code> to 2, you can detect ONLY 2 finger swipes, likewise for 3 fingers.</p>
			
			<button class='btn btn-small btn-info example_btn'>Jump to Example</button>
			<pre class="prettyprint lang-js" data-src="code_1"></pre>
			<span class='navigation'></span>
			
			<div id="test" class="box">Swipe me with 2 fingers</div>

            <div id="test_2" class="box">Swipe me with 3 fingers</div>
			
			<span class='navigation'></span>
		</div>
   </body>
</html>
