<!DOCTYPE HTML>
<html>
    <head>  
    	<!-- 
    		NOTE viewport is set so user can scale 
    		-->
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes"/>
        <meta http-equiv="x-ua-compatible" content="IE=9">
        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />
        
        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
        
        <title>touchSwipe</title>
    </head>
    <body>
		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">
			
			<script id='code_1'>
				$(function() {			
					$("#test").swipe( {
						swipeLeft:function(event, direction, distance, duration, fingerCount) {
							$(this).text("You swiped " + direction );
						},
						fingers:1,	
						threshold:0	
					});
				});
			</script>
			
			<span class='title'></span>
			<h4>property: <span class='properties'><code>fingers</code></span></h4>
			<p>If just one <code>finger</code> is set for swipes, at the meta tag enables user-scaling, then page zooms will bubble up and trigger.</p>
			<pre class="prettyprint lang-html">&lt;meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes"/&gt;</pre>	
	
			<button class='btn btn-small btn-info example_btn'>Jump to Example</button>
			<pre class="prettyprint lang-js" data-src="code_1"></pre>	
			<span class='navigation'></span>
			
			<div id="test" class="box">I only swipe left, but you can pinch zoom me as I only capture 1 finger</div>
			
			<span class='navigation'></span>
		</div>
   </body>
</html>
