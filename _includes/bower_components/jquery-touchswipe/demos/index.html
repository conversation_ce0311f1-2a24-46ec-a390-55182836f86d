<!DOCTYPE HTML>
<html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>

        <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.2.2/css/bootstrap-combined.min.css" rel="stylesheet">
        <link href="http://twitter.github.com/bootstrap/assets/js/google-code-prettify/prettify.css" rel="stylesheet" />
        <link href="css/main.css" type="text/css" rel="stylesheet" />

        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
        <script type="text/javascript" src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
        <script type="text/javascript" src="../jquery.touchSwipe.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>

        <title>touchSwipe</title>

		<script type="text/javascript">
			//Google analyitics
			 var _gaq = _gaq || [];
			  _gaq.push(['_setAccount', 'UA-********-3']);
			  _gaq.push(['_trackPageview']);

			  (function() {
				var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
				ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
				var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
			  })();

		</script>

	</head>
	<body>

		<a href="https://github.com/mattbryson"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>
		<div class="container">
			<div class="page-header">
			  <h1>TouchSwipe <small>a jQuery plugin for touch devices</small></h1>
			  <br/>
			  <iframe src="http://www.facebook.com/plugins/like.php?href=http%3A%2F%2Flabs.skinkers.com%2FtouchSwipe%2F&amp;layout=standard&amp;show_faces=true&amp;width=450&amp;action=like&amp;font&amp;colorscheme=light&amp;height=80" scrolling="no" frameborder="0" style="border:none; overflow:hidden; width:450px; height:80px;" allowTransparency="true"></iframe>
				<p>TouchSwipe is a jquery plugin to be used with jQuery on touch input devices such as iPad, iPhone etc.</p>
			</div>

			<h1>Features</h1>
			<ul>
				<li>Detects swipes in 4 directions, "up", "down", "left" and "right"</li>
				<li>Detects pinches "in" and "out"</li>
				<li>Supports single finger or double finger touch events</li>
				<li>Supports click events both on the touchSwipe object and its child objects</li>
				<li>Definable threshold / maxTimeThreshold to determin when a gesture is actually a swipe</li>
				<li>Events triggered for swipe "start","move","end" and "cancel"</li>
				<li>End event can be triggered either on touch release, or as soon as threshold is met</li>
				<li>Allows swiping and page scrolling</li>
				<li>Disables user input elements (Button, form, text etc) from triggering swipes</li>

			</ul>

			<h1>Docs</h1>
			<ul>
				<li><a href='../docs' target='docs'>Plugin Documentation</a></li>
			</ul>

			<h1>Options</h1>
			<ul>
				<li>See the defaults page in the documentation:  			<a href='../docs/%24.fn.swipe.defaults.html' target='docs'>$.fn.swipe.defaults</a></li>
			</ul>

			<h1>Demos</h1>
			<span class='navigation_list'></span>


		</div>
		<hr/>
	</body>
</html>
