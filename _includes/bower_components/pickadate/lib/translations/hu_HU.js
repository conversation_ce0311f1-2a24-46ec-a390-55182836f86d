// Hungarian

jQuery.extend( jQuery.fn.pickadate.defaults, {
    monthsFull: [ 'janu<PERSON>r', 'febru<PERSON>r', 'má<PERSON><PERSON>', 'ápril<PERSON>', 'm<PERSON><PERSON><PERSON>', 'jún<PERSON>', 'j<PERSON><PERSON>', 'augus<PERSON><PERSON>', 'szeptember', 'október', 'november', 'december' ],
    monthsShort: [ 'jan', 'febr', 'márc', 'ápr', 'máj', 'jún', 'júl', 'aug', 'szept', 'okt', 'nov', 'dec' ],
    weekdaysFull: [ 'vasárnap', 'hétfő', 'kedd', 'szerda', 'csütörtök', 'péntek', 'szombat' ],
    weekdaysShort: [ 'V', 'H', 'K', 'SZe', 'CS', 'P', 'SZo' ],
    today: 'Ma',
    clear: 'Törl<PERSON>',
    firstDay: 1,
    format: 'yyyy. mmmm dd.',
    formatSubmit: 'yyyy/mm/dd'
});

jQuery.extend( jQuery.fn.pickatime.defaults, {
    clear: 'Törl<PERSON>'
});
