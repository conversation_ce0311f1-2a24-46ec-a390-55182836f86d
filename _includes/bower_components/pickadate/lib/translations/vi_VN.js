// Vietnamese

jQuery.extend( jQuery.fn.pickadate.defaults, {
    monthsFull: [ 'Tháng <PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>h<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>háng <PERSON>', 'Tháng <PERSON>' ],
    monthsShort: [ '<PERSON><PERSON><PERSON>', '<PERSON>', 'Ba', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>' ],
    weekdaysFull: [ '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>' ],
    weekdaysShort: [ '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>' ],
    today: '<PERSON><PERSON><PERSON>',
    clear: '<PERSON>o<PERSON>',
    firstDay: 1
});

jQuery.extend( jQuery.fn.pickatime.defaults, {
    clear: 'Xoá'
});
