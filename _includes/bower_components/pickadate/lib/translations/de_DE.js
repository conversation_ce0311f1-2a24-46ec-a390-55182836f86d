// German

jQuery.extend( jQuery.fn.pickadate.defaults, {
    monthsFull: [ '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'August', 'September', '<PERSON><PERSON><PERSON>', 'November', 'Dezember' ],
    monthsShort: [ '<PERSON>', 'Feb', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Jul', 'Aug', '<PERSON>', 'Okt', 'Nov', 'Dez' ],
    weekdaysFull: [ 'Sonntag', 'Mont<PERSON>', '<PERSON><PERSON><PERSON>', 'Mittwo<PERSON>', '<PERSON><PERSON>tag', 'Freitag', 'Samstag' ],
    weekdaysShort: [ 'So', '<PERSON>', 'Di', 'Mi', 'Do', 'Fr', 'Sa' ],
    today: 'Heute',
    clear: '<PERSON><PERSON><PERSON>',
    close: 'Schließen',
    firstDay: 1,
    format: 'dddd, dd. mmmm yyyy',
    formatSubmit: 'yyyy/mm/dd'
});

jQuery.extend( jQuery.fn.pickatime.defaults, {
    clear: '<PERSON><PERSON><PERSON>'
});
