// Lietuviškai

jQuery.extend( jQuery.fn.pickadate.defaults, {
    labelMonthNext: '<PERSON><PERSON><PERSON> mėnuo',
    labelMonthPrev: '<PERSON>ks<PERSON><PERSON> mėnuo',
    labelMonthSelect: 'Pasirinkite mėnesį',
    labelYearSelect: 'Pasirinkite metus',
    monthsFull: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
    monthsShort: ['Sau', 'Vas', 'Kov', 'Bal', 'Geg', 'Bir', 'Lie', 'Rgp', 'Rgs', 'Spa', 'Lap', 'Grd'],
    weekdaysFull: ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>die<PERSON>', 'Antradienis', 'Trečiadienis', '<PERSON><PERSON>virtadien<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>ien<PERSON>'],
    weekdaysShort: ['Sk', 'Pr', 'An', 'Tr', 'Kt', 'Pn', '<PERSON><PERSON>'],
    today: '<PERSON>iandien',
    clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    close: '<PERSON><PERSON><PERSON><PERSON>i',
    firstDay: 1,
    //format: 'yyyy !m. mmmm d !d.', // need to have diffrent case of full months name
    format: 'yyyy-mm-dd',
    formatSubmit: 'yyyy/mm/dd'
});

jQuery.extend( jQuery.fn.pickatime.defaults, {
    clear: 'Išvalyti',
    format: 'HH:i'
});
