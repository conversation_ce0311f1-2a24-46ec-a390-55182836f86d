jQuery.extend(jQuery.fn.pickadate.defaults,{monthsFull:["Tháng <PERSON>","Th<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>á<PERSON>","Th<PERSON><PERSON>","Tháng M<PERSON>ờ<PERSON>","Tháng Mườ<PERSON> Mộ<PERSON>","Tháng Mười Hai"],monthsShort:["<PERSON>ộ<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON>ư<PERSON> M<PERSON>t","Mười Hai"],weekdaysFull:["<PERSON><PERSON> Nhật","Th<PERSON> Hai","Th<PERSON> Ba","T<PERSON><PERSON> Tư","<PERSON><PERSON><PERSON> Năm","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>ả<PERSON>"],weekdaysShort:["<PERSON><PERSON>","<PERSON><PERSON>Hai","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"],today:"<PERSON>ô<PERSON> Nay",clear:"Xo<PERSON>",firstDay:1}),jQ<PERSON>y.extend(jQuery.fn.pickatime.defaults,{clear:"Xoá"});