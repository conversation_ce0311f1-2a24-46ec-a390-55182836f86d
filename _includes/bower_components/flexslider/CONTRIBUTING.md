# How to contribute

Community made patches, localisations, bug reports and contributions are always welcome and are crucial to ensure FlexSlider remains the #1 responsive jQuery slideshow solution. :)

When contributing, please ensure you follow the guidelines below so that we can keep on top of things.

__Note:__ 

GitHub is for *bug reports and contributions only* - if you have a setup question, please be mindful of this and keep your question clear and concise, if you must post it to this repository.

If you are a WooThemes customer, use [WooThemes Support](http://support.woothemes.com). Our ninjas are on hand to assist with your set up query.

## Getting Started

* Make sure you have a [GitHub account](https://github.com/signup/free)
* Submit a ticket for your issue, assuming one does not already exist (please look over the currently open issues before creating a new issue).
  * Clearly describe the issue including steps to reproduce when it is a bug.
  * Make sure you fill in the earliest version that you know has the issue.

## Making Changes

* Fork the repository on GitHub.
* Make the changes to your forked repository.
  * Ensure you use LF line endings - no crazy windows line endings. :)
* When committing, reference your issue (#1234) and include a note about the fix.
* Push the changes to your fork and submit a pull request on the **master** branch of the FlexSlider repository. Existing maintenance branches will be maintained of by FlexSlider developers.
* Please don't modify the changelog, this will be maintained by FlexSlider developers.

At this point you're waiting on us to merge your pull request. We'll review all pull requests, and make suggestions and changes if necessary.

# Additional Resources

* [General GitHub documentation](http://help.github.com/)
* [GitHub pull request documentation](http://help.github.com/send-pull-requests/)
* [WooThemes Support](http://support.woothemes.com)