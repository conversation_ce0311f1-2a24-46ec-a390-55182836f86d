{% if page.is_post %}
  {% assign type = "article" %}
{% else %}
  {% assign type = "hotel" %}
{% endif %}
  <!-- http://ogp.me/ -->
  <meta property="og:type" content="{{ type }}" />
  <meta property="og:url" content="{{ page.url | replace:'index.html','' | prepend: site.baseurl | prepend: site.url }}" />
  <meta property="og:title" content="{% if page.title %}{{ page.title }}{% else %}{{ site.title }}{% endif %}" />
  <meta property="og:description" content="{% if page.excerpt %}{{ page.excerpt | strip_html | strip_newlines | truncate: 140 }}{% else %}{{ site.description }}{% endif %}" />
  {% if page.image %}
  <meta property="og:image" content="{{ site.url }}{{ page.image.path }}" />
  <meta property="og:image:width" content="{{ page.image.width }}" />
  <meta property="og:image:height" content="{{ page.image.height }}" />
  {% endif %}



