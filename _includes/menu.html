<div class="fixed inset-x-0 bottom-0 z-10 w-full  flex bg-white ">

    <div id="navigation" class="w-full flex flex-1 flex-nowrap justify-start items-center bg-white opacity-9">
        <div class=" flex flex-1 justify-around items-center text-xl">
            {% for menu in site.data.menu.mob %} 
                <a class="flex flex-1 h-12 justify-center items-center font-bebas text-3xl  bg-gray-800 text-white  hover:bg-pink-800 hover:text-white focus:bg-pink-800 focus:text-white0" 
                href="{% unless page.section != "frontpage" or menu.href == 'map' %}#{%else%}/{% endunless %}{{ menu.href }}">
                {{ menu.title-mob }}
                </a>
            {% endfor %}
        </div>
    </div>
</div>