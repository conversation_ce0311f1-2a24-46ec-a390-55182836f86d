{% assign rooms = site.rooms | sort: 'order' %}
{% for room in rooms %}
{% unless room.section != page.section and page.section != "frontpage" %}
    <section id="{{ room.section }}"  class=" w-full flex justify-center {{ room.section }} ">
        <div class="rooms-inside container pt-7 pb-8 ">
            <div class="rooms-header px-2 pt-10 mb-6">
                <div class="text-7xl uppercase font-bebas" >{{ room.title }}</div>
                <div class="flex flex-nowrap justify-between">
                    <div class="text-2xl md:text-4xl " >{{ room.subtitle }} </div>
                    <div class="flex justify-evenly items-center">
                        <a 
                        class="flex items-center font-sm text-blue-800 hover:text-purple-800 focus:text-purple-800 hover:underline focus:underline" 
                        href="{{site.baseurl}}/map">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                        class="w-8 md:w-10">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                        </svg><span class="px-1">map</span></a>

                        <div class="px-2 text-blue-800">/</div>

                        <a class="flex items-center font-sm text-blue-800 hover:text-purple-800 focus:text-purple-800 hover:underline focus:underline" 
                        href="https://goo.gl/maps/VCdcMkivN9ZZNYDe7" 
                        target="_blank">
                        <svg class="w-8 md:w-10" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg><span class="px-1">directions</span></a>
                    </div>
                    
                </div>
              </div>
            <div class="md:px-2 mb-6">

{% if room.section == "kriopigi" %}
<div class="video-container container flex w-full sm:flex-wrap" style="margin: 20px auto;">
    <div id="player" data-plyr-provider="youtube" data-plyr-embed-id="In9yQABwOxY"></div>
    <div id="player" data-plyr-provider="youtube" data-plyr-embed-id="XUrdaUDeZ_w"></div>
    <div id="player" data-plyr-provider="youtube" data-plyr-embed-id="XvEM3NbX6Gg"></div>
</div>

<style>
    .plyr {
        width:100%;
        margin: 8px;
    }
    .video-container {
        flex-wrap: wrap;
    }
    @media (min-width: 640px) {
        .plyr {
            width:33%;
            margin: 8px;
        }
        .video-container {
            flex-wrap: nowrap;
        }
    }
</style>
{% endif %}
                 <div class="my-gallery" itemscope itemtype="http://schema.org/ImageGallery">
                  {% assign image_files = site.data.photos.index[room.section].contents %}

                 

                  {% for image in image_files %}
                    {% assign image-basename = image.filename | remove: ".jpg" %}

                    <figure id="{{room.section}}-{{forloop.index}}"
                    itemprop="associatedMedia" 
                    itemscope 
                    itemtype="http://schema.org/ImageObject" 
                    class="shadow-lg 
                    lazy-background
                    {% if forloop.first %}full {%endif%}{% cycle 'large', 'medium' %}">
                    
                    <a href="{{site.baseurl}}/assets/rooms2/{{room.section}}/{{image-basename}}-{% if image.orientation == portrait %}portrait{% else %}full{% endif %}.webp" 
                    itemprop="contentUrl" 
                    data-size="{% if image.orientation == portrait %}{% capture imagewebp %}{{image-basename}}-portrait.webp{% endcapture %}{% else %} {% capture imagewebp %}{{image-basename}}-full.webp{% endcapture %}{% endif %}{% assign imagesize = site.data.photos.index-resized[room.section].contents | where: "filename",imagewebp %}{% for imagedimention in imagesize %} {{imagedimention.width}}x{{imagedimention.height}}{% endfor %}">
                    <img 
                    src="{{site.baseurl}}/assets/rooms2/{{room.section}}/{{image-basename}}-thumb.webp" 
                    itemprop="thumbnail" width="1" height="1"
                    alt="Babis Apartments / {{ room.section }}-{{ forloop.index }}" />
                    </a>
                    <figcaption itemprop="caption description">Babis Apartments / {{ room.section }}-{{ forloop.index }}</figcaption>
                    </figure>
                  {% endfor %}

                  </div>
            </div>
                <div class="column-right">
                    <div class="rooms-description"> {{ room.content }} </div>
                   

                     

                            <div class="flex mb-6">
                            <!-- First colon table -->

                                <div class="w-36 md:w-1/5">
                                    <div class="bg-gray-500 h-12 md:h-14 uppercase font-bebas md:text-2xl border-r border-white lg:text-4xl text-xl text-white leading-5  bold flex items-center justify-center">{{room.title}} - {{room.year}} </div>
                                    {% for season in site.data.season.seasons.dates %}
                                    <div class="bg-gray-500  flex justify-end py-4 pr-4 border-t border-white text-white text-sm md:text-xl bold text-right"> {{ season.row }}</div>
                                    {% endfor %}
                                </div>

                            <div class="w-full md:w-4/5 flex">
                                <div class="w-1/3 flex-1">
                                    <div class="h-12 md:h-14 bg-gray-500 flex items-center justify-center  border-r border-white text-white text-sm md:text-xl text-center ">{{ room.room1}}</div>
                                    {% for roomprice in room.prices.two %}
                                    <div class=" py-4 pr-2 text-black text-center border-t {%if forloop.last %}border-b{%endif%} border-white text-sm md:text-xl "> {{ roomprice.price }}€ </div>
                                    {% endfor %}
                                </div>
                                <div class="w-1/3 flex-1">
                                    <div class="h-12 md:h-14 bg-gray-500 flex items-center justify-center  border-r border-white text-white text-sm md:text-xl text-center">{{ room.room2}}</div>
                                    {% for roomprice in room.prices.three %}
                                    <div class=" py-4 pr-2 text-black text-center border-t {%if forloop.last %}border-b{%endif%} border-white text-sm md:text-xl "> {{ roomprice.price }}€ </div>
                                    {% endfor %}
                                </div>
                                <div class="w-1/3 flex-1">
                                    <div class="h-12 md:h-14 bg-gray-500 flex items-center justify-center  border-r border-white  text-white text-sm md:text-xl text-center">{{ room.room3}}</div>
                                    {% for roomprice in room.prices.four %}
                                    
                                    <div class=" py-4 pr-2 text-black text-center border-t {%if forloop.last %}border-b{%endif%} border-white text-sm md:text-xl "> {{ roomprice.price }}€ </div>
                                    {% endfor %}
                                </div>
                            </div>
                </div>
<p class="p-4">*The above prices are valid for a minimum stay of 2 nights</p>

         
                </div>
                </div>
        </section>
        {% endunless %}
{% endfor %}
