

<div class="hero w-full relative" style="margin-bottom:40vh;">
        
    <div class="hero-static">
        <h1 class="text-white text-opacity-0">{{page.title}}</h1>

    </div>

     <div 
     class="h-full w-full absolute flex justify-center items-center content-center inset-y-0" 
     style="height:40vh; z-index:9999;">
     {% if room.section != page.section and page.section != "frontpage" %}
            <a class="flex-1 h-full text-white text-opacity-0   bg-white
            {% if page.section == "kriopigi" %} bg-opacity-50 {% else %}  opacity-0 hover:opacity-50 focus:opacity-50 {% endif %}" 
            href="/kriopigi">kriopigi</a>
            <a class="flex-1 h-full text-white text-opacity-0   bg-white
            {% if page.section == "afitos" %} bg-opacity-50 {% else %}  opacity-0 hover:opacity-50 focus:opacity-50 {% endif %}" 
            href="/afitos">afitos</a>
         {% else %}
         <a class="flex-1 h-full text-white text-opacity-0 bg-white opacity-0 focus:opacity-50 hover:opacity-50" href="#kriopigi">kriopigi</a>
         <a class="flex-1 h-full text-white text-opacity-0 bg-white  opacity-0 focus:opacity-50 hover:opacity-50" href="#afitos">afitos</a>
    {%endif%}
     </div>
     
</div>