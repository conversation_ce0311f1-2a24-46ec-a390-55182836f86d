# This file is a template, and might need editing before it works on your project.
# Template project: https://gitlab.com/pages/jekyll
# Docs: https://docs.gitlab.com/ce/pages/
image: ruby:2.3

variables:
  JEKYLL_ENV: production

before_script:
- bundle config disable_checksum_validation true
- bundle install

test:
  stage: test
  script:
  - bundle exec jekyll build -d test
  artifacts:
    paths:
    - test
  except:
  - master

pages:
  stage: deploy
  script:
  - bundle exec jekyll build -d public --config _config.yml
  variables:
    JEKYLL_ENV: production
    LANG: "C.UTF-8"
  artifacts:
    paths:
    - public
  only:
  - master

