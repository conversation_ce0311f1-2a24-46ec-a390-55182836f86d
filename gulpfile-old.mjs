// https://gist.github.com/noraj/007a943dc781dc8dd3198a29205bae04

import gulp from 'gulp';
const { series, parallel, src, dest, task } = gulp;
import browserSync    from 'browser-sync';
// import responsive     from 'gulp-responsive'; // Removed - using sharp directly
import image          from 'gulp-image';
import rename         from 'gulp-rename';
import fs             from 'node:fs';
import concat         from 'gulp-concat';
import log            from 'fancy-log';
import gulpif         from 'gulp-if';
import recursiveReadSync from 'recursive-readdir-sync';
import imgsize        from 'image-size';
import merge          from 'deepmerge';
import yaml           from 'js-yaml';
import pump           from 'pump';
import uglify         from 'gulp-uglify';
import purify         from 'gulp-purifycss';
import nano           from 'gulp-cssnano';
import include        from 'gulp-include';
import rollup         from 'gulp-rollup';
import sass           from 'gulp-sass';
import * as dartSass  from 'sass';
import postcss        from 'gulp-postcss';
import autoprefixer   from 'autoprefixer';
import pxtorem        from 'postcss-pxtorem';
import orderedValues  from 'postcss-ordered-values';
import colorHexAlpha  from "postcss-color-hex-alpha";
import responsiveType from "postcss-responsive-type";
import {createDebugger} from 'postcss-debug';

// Configure gulp-sass to use Dart Sass
const sassCompiler = sass(dartSass);

gulp.task('images', function () {
  gulp.src('./assets/**/*.*')
   .pipe(image({
        pngquant: true,
        optipng: false,
        zopflipng: true,
        jpegRecompress: false,
        jpegoptim: true,
        mozjpeg: true,
        gifsicle: true,
        svgo: true,
        concurrent: 10
      }))
    .pipe(gulp.dest('./assets'));
});

// Commented out - use gulpfile-modern.mjs for modern sharp-based implementation
/*
gulp.task('images-all-webp-slideshow', function () {
    'use strict';
  return gulp.src(['_resources/uploaded/slideshow/**/*.{png,jpg,jpeg}'])
      // Use configuration
      .pipe(responsive({
        '**/*.{png,jpg}': [{
          width: 1,
          height: 1,
          rename: {
            suffix: '-thumb',
            extname: '.webp'},
          format: 'webp',
        },
        {
          width: 268,
          rename: {
            suffix: '-268' ,
            extname: '.webp'},
          format: 'webp',
        },
        {
          width: 552,
          height:330,
          rename: {
            suffix: '-552' ,
            extname: '.webp'},
          format: 'webp',
        },
        {
          height: 960,
          height:330,
          rename: {
            suffix: '-960' ,
            extname: '.webp'},
          format: 'webp',
        },
        {
          width: 1440,
          rename: {
            suffix: '-1440' ,
            extname: '.webp'},
          format: 'webp',
        },
        {
            width: 2000,
            rename: {
              suffix: '-2000' ,
              extname: '.webp'},
            format: 'webp',
          }]
      },{
        errorOnEnlargement: false,
        quality: 80,
        withMetadata: false,
        compressionLevel: 7
      }))
      .pipe(gulp.dest('assets/sllideshow/'));
  });
*/

/*
  gulp.task('images-all-webp-placeholder', function () {
    'use strict';
  return gulp.src(['_resources/uploaded/rooms/placeholder/**/*.{png,jpg}'])
      // Use configuration
      .pipe(responsive({
        '**/*.{png,jpg}': [{
          width: 1,
          height: 1,
          rename: {
            suffix: '-thumb',
            extname: '.webp'},
          format: 'webp',
        },
        {
          width: 268,
          rename: {
            suffix: '-medium' ,
            extname: '.webp'},
          format: 'webp',
        },
        {
          width: 552,
          rename: {
            suffix: '-large' ,
            extname: '.webp'},
          format: 'webp',
        },
        {
          height: 960,
          rename: {
            suffix: '-portrait' ,
            extname: '.webp'},
          format: 'webp',
        },
        {
          width: 1440,
          rename: {
            suffix: '-full' ,
            extname: '.webp'},
          format: 'webp',
        }]
      },{
        errorOnEnlargement: false,
        quality: 80,
        withMetadata: false,
        compressionLevel: 7
      }))
      .pipe(gulp.dest('assets/rooms2/placeholder'));
  });
*/

/*
gulp.task('images-all-webp', function () {
  'use strict';
return gulp.src(['_resources/uploaded/rooms/**/*.{png,jpg}'])
    // Use configuration
    .pipe(responsive({
      '**/*.{png,jpg}': [{
        width: 1,
        height: 1,
        rename: {
          suffix: '-thumb',
          extname: '.webp'},
        format: 'webp',
      },
      {
        width: 268,
        rename: {
          suffix: '-medium' ,
          extname: '.webp'},
        format: 'webp',
      },
      {
        width: 552,
        rename: {
          suffix: '-large' ,
          extname: '.webp'},
        format: 'webp',
      },
      {
        height: 960,
        rename: {
          suffix: '-portrait' ,
          extname: '.webp'},
        format: 'webp',
      },
      {
        width: 1440,
        rename: {
          suffix: '-full' ,
          extname: '.webp'},
        format: 'webp',
      }]
    },{
      errorOnEnlargement: false,
      quality: 80,
      withMetadata: false,
      compressionLevel: 7
    }))
    .pipe(gulp.dest('assets/rooms2/'));
});
*/

/*
gulp.task('images-all-jpg', function () {
  'use strict';
return gulp.src(['_resources/uploaded/rooms/**/*.{png,jpg}'])
    // Use configuration
    .pipe(responsive({
      '**/*.{png,jpg}': [{
        width: 1,
        height: 1,
        rename: {
          suffix: '-thumb'}
      },
      {
        width: 268,
        rename: {
          suffix: '-medium' }
      },
      {
        width: 552,
        rename: {
          suffix: '-large' }
      },
      {
        height: 960,
        rename: {
          suffix: '-portrait' }
      },
      {
        width: 1440,
        rename: {
          suffix: '-full' }
      }]
    },{
      errorOnEnlargement: false,
      quality: 80,
      withMetadata: false,
      compressionLevel: 7
    }))
    .pipe(gulp.dest('assets/rooms2/'));
});
*/

/*
       _           _
 _ __ | |__   ___ | |_ ___  ___
| '_ \| '_ \ / _ \| __/ _ \/ __|
| |_) | | | | (_) | || (_) \__ \
| .__/|_| |_|\___/ \__\___/|___/
|_|
*/

// Containers for image data processing which is kicked off by gulp
// but aren't actually gulp tasks. Adapted from http://stackoverflow.com/a/18934385
// We don't need a recursive function since we know the structure.
// Create object: {
//   'album name' : {
//     'title': (directory name without the date)
//     'date': (directory name without the name)
//     'contents': [ (an array of photo objects, to be sorted by date)
//       {
//         properties pulled from EXIF data and image size
//       }
//     ]
// }
const walkPhotos = (path, index) => {
  const directory = fs.readdirSync(path);

  // Directory is going to be an array of album directories
  for (var i = 0; i < directory.length; i++) {
    // This is the directory name from Lightroom ('2015-12-31 New Years Eve' style)
    const album = directory[i];

    // This is the directory shortname Gulp is using for image output.
    const dirname = album;

    // This will be the image contents and any subdirectories
    const photos = recursiveReadSync(path + '/' + album);
    const contains = [];

    for (var j = 0; j < photos.length; j++) {
      // recursiveReadSync returns the path relative to the CWD, not just the name
      // like fs.readdirSync so this will be /source/Photography/.../whatever.img
      const photo = photos[j];

      // So split on / and take the last component for the filename.
      const file = photo.split('/').pop();

      // Original images are sometimes in subdirectories by day or activity, which
      // is why we recused the whole thing. Don't try to get stats on a directory,
      // just skip it.
      if (fs.statSync(photo).isDirectory()) { continue; }

      const dimensions = imgsize(photo);

      const photoBuffer = fs.readFileSync(photo);


      var orientvar = 'landscape'

      if (dimensions.width < dimensions.height) {
         var orientvar = 'portrait';
      }


      contains.push({
        filename: file,
        width: dimensions.width || null,
        height: dimensions.height || null,
        orientation: orientvar
      });
    }

    index[dirname] = {
      contents: contains
    };
  }

  // Now sort all photos in each album by the date of the exposure instead
  // of the name. We do this here because:
  // - The existing index file (which has custom data) is already sorted
  // - Sorted albums are arrays, not objects. So if the order here doesn't
  //   match what's in the generated file, custom attributes will be applied
  //   to the wrong image when merging (because arrays are indexed, not keyed).
  //   ^^ @TODO: That'll fix most of the issue, but inserting/deleting within
  //      an existing album will still cause attributes to shift. :(
  for (var album in index) {
    if (!index.hasOwnProperty(album)) { continue; }
    index[album].contents = index[album].contents.sort((a, b) => {
      if (a.date < b.date) { return -1; }
      if (a.date > b.date) { return 1; }
      return 0;
    });
  }
};


gulp.task('photos', function () {
  let index = {};
  const generatedIndex = {};
  try {
    index = fs.readFileSync('_data/photos/index.yml', {encoding: 'utf8'});
    index = yaml.safeLoad(index);
  }
  catch (e) {
    if (e.code === 'ENOENT') {
      log('No original index found; will create one.');
    }
    else {
      throw e;
    }
  }
  walkPhotos('_resources/uploaded/rooms', generatedIndex);
  const mergedIndex = merge(index, generatedIndex);

  fs.writeFileSync('_data/photos/index.yml', yaml.safeDump(mergedIndex));
  return new Promise(function(resolve, reject) {
    console.log("Photos indexed");
    resolve();
  });
});

gulp.task('photos-resized', function () {
  let index = {};
  const generatedIndex = {};
  walkPhotos('assets/rooms2', generatedIndex);
  const mergedIndex = merge(index, generatedIndex);

  fs.writeFileSync('_data/photos/index-resized.yml', yaml.safeDump(mergedIndex));
});

gulp.task('js-compress', function (cb) {
  pump([
      gulp.src('_site/js/js-all.js'),
      uglify(),
      gulp.dest('assets/js/optimized')
    ],
    cb
  );
});


gulp.task('maincss', function() {
  return gulp.src(['./_site/assets/css/styles.css'])
    .pipe(gulp.dest('assets/css/optimized'))
    .pipe(gulp.dest('_includes/css'));
});

gulp.task('js', function() {
    return gulp.src(['./assets/js/_entry.js'])
    .pipe(include({
        extensions: 'js',
        hardFail: true,
        separateInputs: true,
        includePaths: [
          __dirname + '/node_modules'
        ]
      }))
    .pipe(rename('js-all.js'))
    .pipe(gulp.dest('assets/js/optimized'))
  });

  gulp.task('js-mappages', function() {
    return gulp.src(['./assets/js/_entrymappages.js'])
    .pipe(include({
        extensions: 'js',
        hardFail: true,
        separateInputs: true,
        includePaths: [
          __dirname + '/node_modules'
        ]
      }))
    .pipe(rename('js-all-mappages.js'))
    .pipe(gulp.dest('assets/js/optimized'))
  });


  gulp.task('bundle', function() {
    return gulp.src('assets/js/optimized/js-all.js')
      // transform the files here.
      .pipe(rollup({
        // any option supported by Rollup can be set here.
        input: 'assets/js/optimized/js-all.js',
        output: {
            file: 'bundle.js',
            format: 'cjs'
          }
      }))
      .pipe(gulp.dest('assets/js/optimized'))
  });
