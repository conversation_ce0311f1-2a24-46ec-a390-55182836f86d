{"dependencies": {"@fullhuman/postcss-purgecss": "^5.0.0", "autoprefixer": "^10.1.0", "browser-sync": "^2.26.13", "cookieconsent": "3", "cssnano": "^4.1.10", "deepmerge": "^4.2.2", "fs": "0.0.1-security", "gulp-cli": "^3.0.0", "gulp-concat": "^2.6.1", "gulp-cssnano": "^2.1.3", "gulp-if": "^3.0.0", "gulp-image": "^6.2.1", "gulp-include": "^2.4.1", "gulp-postcss": "^9.0.0", "gulp-purifycss": "^0.2.0", "gulp-rename": "^2.0.0", "gulp-rollup": "^2.17.0", "gulp-sass": "^5.1.0", "gulp-uglify": "^3.0.2", "gulp-util": "^3.0.8", "image-size": "^0.9.3", "js-yaml": "^3.14.1", "mapbox-gl": "^2.0.1", "npm-install-all": "^1.1.21", "photoswipe": "^4.1.3", "postcss-cli": "^9.0.2", "postcss-color-hex-alpha": "^6.0.0", "postcss-debug": "^0.4.2", "postcss-easy-import": "^3.0.0", "postcss-import": "^14.0.0", "postcss-ordered-values": "^4.1.2", "postcss-pxtorem": "^5.1.1", "postcss-responsive-type": "^1.0.0", "pump": "^3.0.0", "recursive-readdir-sync": "^1.0.6", "sass": "^1.69.0", "tailwindcss": "^2.0.2"}, "devDependencies": {"gulp": "^4.0.2", "postcss": "^8.4.4", "postcss-nested": "^5.0.3", "postcss-scss": "^3.0.4"}}